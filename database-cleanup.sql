-- DATABASE CLEANUP - Remove Unused Columns and Tables
-- Run this in Supabase SQL Editor

-- 1. Remove unused columns from user_profiles table
ALTER TABLE user_profiles DROP COLUMN IF EXISTS workout_types;
ALTER TABLE user_profiles DROP COLUMN IF EXISTS goals;

-- 2. Drop recovery_plans table (unused functionality)
DROP TABLE IF EXISTS recovery_plans CASCADE;

-- 3. Verify the cleaned up user_profiles structure
-- SELECT column_name, data_type, is_nullable 
-- FROM information_schema.columns 
-- WHERE table_name = 'user_profiles' 
-- ORDER BY ordinal_position;

-- 4. Verify workouts table structure
-- SELECT column_name, data_type, is_nullable 
-- FROM information_schema.columns 
-- WHERE table_name = 'workouts' 
-- ORDER BY ordinal_position;

-- 5. Verify exercise_stats table structure  
-- SELECT column_name, data_type, is_nullable 
-- FROM information_schema.columns 
-- WHERE table_name = 'exercise_stats' 
-- ORDER BY ordinal_position;

-- Expected final structure:
-- user_profiles: id, user_id, name, age, gender, height, weight, created_at, updated_at
-- workouts: id, user_id, name, exercises, last_performed, created_at
-- exercise_stats: id, user_id, exercise, muscle_group, max_weight, max_reps, created_at, updated_at
