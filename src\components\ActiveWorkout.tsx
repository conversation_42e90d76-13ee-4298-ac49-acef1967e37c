import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import NavigationHeader from './NavigationHeader';

interface SavedWorkout {
  id: string;
  name: string;
  exercises: Array<{
    muscleGroup: string;
    exercise: string;
  }>;
  createdAt: string;
  lastPerformed?: string;
}

interface ExerciseRecommendation {
  exercise: string;
  muscleGroup: string;
  recommendedWeight: number;
  recommendedReps: number;
  recommendedSets: number;
  reasoning: string;
}

interface UserProfile {
  name: string;
  age: number;
  gender: string;
  height: { value: number; unit: string; feet?: number; inches?: number };
  weight: { value: number; unit: string };
  workoutTypes: string[];
  goals: string[];
}

const ActiveWorkout: React.FC = () => {
  const navigate = useNavigate();
  const [workout, setWorkout] = useState<SavedWorkout | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [daysSinceLastWorkout, setDaysSinceLastWorkout] = useState<number | ''>('');
  const [lastWorkoutDate, setLastWorkoutDate] = useState<string>('');
  const [recommendations, setRecommendations] = useState<ExerciseRecommendation[]>([]);
  const [showRecommendations, setShowRecommendations] = useState(false);

  // Calculate days since last workout from date
  const calculateDaysFromDate = (dateString: string) => {
    if (!dateString) return 0;
    const selectedDate = new Date(dateString);
    const today = new Date();
    const diffTime = Math.abs(today.getTime() - selectedDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  // Handle date change
  const handleDateChange = (dateString: string) => {
    setLastWorkoutDate(dateString);
    const days = calculateDaysFromDate(dateString);
    setDaysSinceLastWorkout(days);
  };

  useEffect(() => {
    // Load active workout
    const activeWorkout = localStorage.getItem('activeWorkout');
    if (activeWorkout) {
      const workoutData = JSON.parse(activeWorkout);
      setWorkout(workoutData);
    } else {
      navigate('/dashboard');
    }

    // Load user profile
    const profile = localStorage.getItem('userProfileData');
    if (profile) {
      setUserProfile(JSON.parse(profile));
    }
  }, [navigate]);

  const generateRecommendations = () => {
    if (!workout || daysSinceLastWorkout === '') return;

    const days = Number(daysSinceLastWorkout);

    // AI-powered recommendation algorithm (simulated)
    const exerciseRecommendations = workout.exercises.map(exercise => {
      // Base recommendations by exercise type
      const baseRecommendations: { [key: string]: { weight: number; reps: number; sets: number } } = {
        // Chest
        'Bench Press': { weight: 135, reps: 8, sets: 3 },
        'Incline Press': { weight: 115, reps: 10, sets: 3 },
        'Decline Press': { weight: 125, reps: 8, sets: 3 },
        'Dumbbell Flyes': { weight: 25, reps: 12, sets: 3 },
        'Push-ups': { weight: 0, reps: 15, sets: 3 },
        'Chest Dips': { weight: 0, reps: 10, sets: 3 },

        // Back
        'Pull-ups': { weight: 0, reps: 8, sets: 3 },
        'Lat Pulldowns': { weight: 120, reps: 10, sets: 3 },
        'Rows': { weight: 135, reps: 8, sets: 3 },
        'Deadlifts': { weight: 185, reps: 5, sets: 3 },
        'T-Bar Rows': { weight: 90, reps: 10, sets: 3 },
        'Cable Rows': { weight: 100, reps: 12, sets: 3 },

        // Shoulders
        'Overhead Press': { weight: 95, reps: 8, sets: 3 },
        'Lateral Raises': { weight: 15, reps: 12, sets: 3 },
        'Front Raises': { weight: 15, reps: 12, sets: 3 },
        'Rear Delt Flyes': { weight: 12, reps: 15, sets: 3 },
        'Shrugs': { weight: 135, reps: 12, sets: 3 },
        'Arnold Press': { weight: 30, reps: 10, sets: 3 },

        // Arms
        'Bicep Curls': { weight: 30, reps: 12, sets: 3 },
        'Tricep Dips': { weight: 0, reps: 12, sets: 3 },
        'Hammer Curls': { weight: 25, reps: 12, sets: 3 },
        'Tricep Extensions': { weight: 60, reps: 12, sets: 3 },
        'Preacher Curls': { weight: 25, reps: 10, sets: 3 },
        'Close-Grip Press': { weight: 115, reps: 10, sets: 3 },

        // Legs
        'Squats': { weight: 185, reps: 8, sets: 3 },
        'Lunges': { weight: 25, reps: 12, sets: 3 },
        'Leg Press': { weight: 270, reps: 12, sets: 3 },
        'Calf Raises': { weight: 135, reps: 15, sets: 3 },
        'Leg Curls': { weight: 80, reps: 12, sets: 3 },
        'Leg Extensions': { weight: 90, reps: 12, sets: 3 },

        // Core
        'Planks': { weight: 0, reps: 60, sets: 3 }, // seconds
        'Crunches': { weight: 0, reps: 20, sets: 3 },
        'Russian Twists': { weight: 15, reps: 20, sets: 3 },
        'Mountain Climbers': { weight: 0, reps: 20, sets: 3 },
        'Dead Bug': { weight: 0, reps: 10, sets: 3 },
        'Bicycle Crunches': { weight: 0, reps: 20, sets: 3 }
      };

      const base = baseRecommendations[exercise.exercise] || { weight: 50, reps: 10, sets: 3 };

      // Adjust based on user profile
      let weightMultiplier = 1;
      let repMultiplier = 1;

      // Gender adjustments
      if (userProfile?.gender === 'Female') {
        weightMultiplier *= 0.75;
      }

      // Age adjustments
      if (userProfile?.age && userProfile.age > 50) {
        weightMultiplier *= 0.9;
        repMultiplier *= 1.1;
      } else if (userProfile?.age && userProfile.age < 25) {
        weightMultiplier *= 1.1;
      }

      // Days since last workout adjustments
      let timeMultiplier = 1;
      let reasoning = '';

      if (days <= 3) {
        timeMultiplier = 1.05;
        reasoning = 'Recent workout - slight progression recommended';
      } else if (days <= 7) {
        timeMultiplier = 1.0;
        reasoning = 'Good timing - maintain current intensity';
      } else if (days <= 14) {
        timeMultiplier = 0.9;
        reasoning = 'Moderate deload - ease back into it';
      } else if (days <= 30) {
        timeMultiplier = 0.8;
        reasoning = 'Significant deload - focus on form';
      } else {
        timeMultiplier = 0.7;
        reasoning = 'Extended break - start conservatively';
      }

      const finalWeight = Math.round(base.weight * weightMultiplier * timeMultiplier);
      const finalReps = Math.round(base.reps * repMultiplier);

      return {
        exercise: exercise.exercise,
        muscleGroup: exercise.muscleGroup,
        recommendedWeight: Math.max(finalWeight, base.weight > 0 ? 5 : 0),
        recommendedReps: Math.max(finalReps, 1),
        recommendedSets: base.sets,
        reasoning
      };
    });

    setRecommendations(exerciseRecommendations);
    setShowRecommendations(true);

    // Generate comprehensive data for AI
    const aiData = {
      userProfile,
      workout: {
        name: workout.name,
        daysSinceLastWorkout: days,
        exercises: workout.exercises
      },
      recommendations: exerciseRecommendations,
      generatedAt: new Date().toISOString()
    };

    console.log('=== AI-POWERED WORKOUT RECOMMENDATIONS ===');
    console.log(JSON.stringify(aiData, null, 2));
  };

  const handleFinishWorkout = () => {
    // Update the workout's last performed date
    if (workout) {
      const savedWorkouts = JSON.parse(localStorage.getItem('savedWorkouts') || '[]');
      const updatedWorkouts = savedWorkouts.map((w: SavedWorkout) =>
        w.id === workout.id ? { ...w, lastPerformed: new Date().toISOString() } : w
      );
      localStorage.setItem('savedWorkouts', JSON.stringify(updatedWorkouts));
    }

    // Clear active workout
    localStorage.removeItem('activeWorkout');

    // Navigate back to dashboard
    navigate('/dashboard');
  };

  if (!workout || !userProfile) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <div className="text-white text-xl">Loading workout...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      <NavigationHeader />
      <div className="p-4 md:p-8 pb-24 md:pb-16">
        <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            🚀 <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400">{workout.name}</span>
          </h1>
          <p className="text-purple-200 text-lg">
            {!showRecommendations ? 'How long since you last did this workout?' : 'Your AI-powered recommendations'}
          </p>
        </div>

        <div className="bg-white/10 backdrop-blur-lg rounded-3xl shadow-2xl border border-white/20 p-8">
          {!showRecommendations ? (
            <>
              {/* Days Since Last Workout Input */}
              <div className="text-center mb-8">
                <div className="max-w-md mx-auto">
                  <h2 className="text-2xl font-bold text-white mb-6">
                    When did you last do <span className="text-purple-400">"{workout.name}"</span>?
                  </h2>

                  <div className="mb-6">
                    <label className="block text-purple-300 text-sm font-medium mb-3">
                      Select the date of your last workout:
                    </label>
                    <input
                      type="date"
                      value={lastWorkoutDate}
                      onChange={(e) => handleDateChange(e.target.value)}
                      max={new Date().toISOString().split('T')[0]} // Can't select future dates
                      className="w-full px-6 py-4 bg-white/10 border border-white/20 rounded-2xl shadow-lg backdrop-blur-sm text-white focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300 hover:bg-white/15 text-xl text-center font-semibold [color-scheme:dark]"
                    />
                    {daysSinceLastWorkout > 0 && (
                      <p className="text-purple-400 text-sm mt-3 text-center">
                        That was <span className="font-bold text-white">{daysSinceLastWorkout}</span> days ago
                      </p>
                    )}
                  </div>

                  <p className="text-purple-300 text-sm mb-8">
                    This helps our AI provide personalized recommendations based on your recovery time
                  </p>
                </div>
              </div>

              {/* Workout Preview */}
              <div className="mb-8">
                <h3 className="text-xl font-semibold text-white mb-4">Today's Workout Includes:</h3>
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {workout.exercises.map((exercise, index) => (
                    <div key={index} className="bg-white/5 rounded-xl border border-white/10 p-3">
                      <div className="text-purple-300 text-sm">{exercise.muscleGroup}</div>
                      <div className="text-white font-medium">{exercise.exercise}</div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex justify-center">
                <button
                  onClick={generateRecommendations}
                  disabled={!lastWorkoutDate || daysSinceLastWorkout === 0}
                  className="bg-gradient-to-r from-purple-600 to-pink-600 text-white py-4 px-12 rounded-2xl hover:from-purple-700 hover:to-pink-700 focus:outline-none focus:ring-4 focus:ring-purple-500/50 transition-all duration-300 font-semibold text-lg shadow-xl shadow-purple-500/25 hover:shadow-2xl hover:shadow-purple-500/40 transform hover:scale-[1.02] active:scale-[0.98] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                >
                  🤖 Get AI Recommendations
                </button>
              </div>
            </>
          ) : (
            <>
              {/* AI Recommendations */}
              <div className="mb-6">
                <h2 className="text-2xl font-bold text-white mb-4">Your AI-Powered Recommendations</h2>
                <p className="text-purple-200">
                  Based on {daysSinceLastWorkout} days since your last "{workout.name}" workout
                </p>
              </div>

              <div className="space-y-6">
                {recommendations.map((rec, index) => (
                  <div key={index} className="bg-white/5 rounded-2xl border border-white/10 p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-xl font-semibold text-white">
                        {rec.exercise}
                      </h3>
                      <span className="text-purple-300 text-sm bg-purple-500/20 px-3 py-1 rounded-full border border-purple-500/30">
                        {rec.muscleGroup}
                      </span>
                    </div>

                    <div className="grid md:grid-cols-3 gap-4 mb-4">
                      <div className="bg-green-500/10 border border-green-500/20 rounded-xl p-4 text-center">
                        <div className="text-green-300 text-sm font-medium mb-1">Recommended Weight</div>
                        <div className="text-white text-2xl font-bold">
                          {rec.recommendedWeight > 0 ? `${rec.recommendedWeight} lbs` : 'Bodyweight'}
                        </div>
                      </div>

                      <div className="bg-blue-500/10 border border-blue-500/20 rounded-xl p-4 text-center">
                        <div className="text-blue-300 text-sm font-medium mb-1">Recommended Reps</div>
                        <div className="text-white text-2xl font-bold">
                          {rec.recommendedReps} {rec.exercise === 'Planks' ? 'sec' : 'reps'}
                        </div>
                      </div>

                      <div className="bg-purple-500/10 border border-purple-500/20 rounded-xl p-4 text-center">
                        <div className="text-purple-300 text-sm font-medium mb-1">Recommended Sets</div>
                        <div className="text-white text-2xl font-bold">{rec.recommendedSets} sets</div>
                      </div>
                    </div>

                    <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-xl p-3">
                      <div className="text-yellow-300 text-sm font-medium mb-1">AI Reasoning:</div>
                      <div className="text-yellow-200 text-sm">{rec.reasoning}</div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-8 flex gap-4">
                <button
                  onClick={() => setShowRecommendations(false)}
                  className="flex-1 bg-white/10 text-white py-4 px-8 rounded-2xl hover:bg-white/20 focus:outline-none focus:ring-4 focus:ring-white/25 transition-all duration-300 font-semibold text-lg border border-white/20"
                >
                  ← Back
                </button>
                <button
                  onClick={handleFinishWorkout}
                  className="flex-1 bg-gradient-to-r from-green-600 to-emerald-600 text-white py-4 px-8 rounded-2xl hover:from-green-700 hover:to-emerald-700 focus:outline-none focus:ring-4 focus:ring-green-500/50 transition-all duration-300 font-semibold text-lg shadow-xl shadow-green-500/25 hover:shadow-2xl hover:shadow-green-500/40 transform hover:scale-[1.02] active:scale-[0.98]"
                >
                  ✅ Finish Workout
                </button>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  </div>
  );
};

export default ActiveWorkout;
