import { supabase } from '../lib/supabase'
import type { UserProfile, Workout, ExerciseStats, RecoveryPlan } from '../lib/supabase'

// User Profile Services
export const profileService = {
  async getProfile(userId: string): Promise<UserProfile | null> {
    console.log('🔍 Fetching profile for user:', userId);

    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        console.log('📭 No profile found for user (expected for new users)');
        return null;
      } else {
        console.error('❌ Database error fetching profile:', error);
        console.error('Error details:', {
          code: error.code,
          message: error.message,
          details: error.details,
          hint: error.hint,
          status: error.status
        });
        // For 406 errors, treat as no profile found
        if (error.status === 406 || error.code === '42501') {
          console.log('🔧 Treating 406/permission error as no profile found');
          return null;
        }
        throw error;
      }
    }

    console.log('✅ Profile found:', data);
    return data;
  },

  async createProfile(profile: Omit<UserProfile, 'id' | 'created_at' | 'updated_at'>): Promise<UserProfile> {
    console.log('🔄 Creating profile in database:', profile);

    const { data, error } = await supabase
      .from('user_profiles')
      .insert(profile)
      .select()
      .single()

    if (error) {
      console.error('❌ Database error creating profile:', error);
      console.error('Error details:', {
        code: error.code,
        message: error.message,
        details: error.details,
        hint: error.hint
      });
      throw new Error(`Database error: ${error.message} (Code: ${error.code})`);
    }

    console.log('✅ Profile created successfully:', data);
    return data;
  },

  async updateProfile(userId: string, updates: Partial<UserProfile>): Promise<UserProfile | null> {
    const { data, error } = await supabase
      .from('user_profiles')
      .update(updates)
      .eq('user_id', userId)
      .select()
      .single()
    
    if (error) {
      console.error('Error updating profile:', error)
      return null
    }
    
    return data
  }
}

// Exercise Stats Services
export const exerciseStatsService = {
  async getStats(userId: string): Promise<ExerciseStats[]> {
    const { data, error } = await supabase
      .from('exercise_stats')
      .select('*')
      .eq('user_id', userId)
    
    if (error) {
      console.error('Error fetching exercise stats:', error)
      return []
    }
    
    return data || []
  },

  async updateStats(userId: string, exercise: string, muscleGroup: string, maxWeight: number, maxReps: number): Promise<ExerciseStats | null> {
    const { data, error } = await supabase
      .from('exercise_stats')
      .upsert({
        user_id: userId,
        exercise,
        muscle_group: muscleGroup,
        max_weight: maxWeight,
        max_reps: maxReps
      })
      .select()
      .single()
    
    if (error) {
      console.error('Error updating exercise stats:', error)
      return null
    }
    
    return data
  }
}

// Workout Services
export const workoutService = {
  async getWorkouts(userId: string): Promise<Workout[]> {
    const { data, error } = await supabase
      .from('workouts')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
    
    if (error) {
      console.error('Error fetching workouts:', error)
      return []
    }
    
    return data || []
  },

  async createWorkout(workout: Omit<Workout, 'id' | 'created_at'>): Promise<Workout | null> {
    const { data, error } = await supabase
      .from('workouts')
      .insert(workout)
      .select()
      .single()
    
    if (error) {
      console.error('Error creating workout:', error)
      return null
    }
    
    return data
  }
}

// Recovery Plan Services
export const recoveryPlanService = {
  async saveRecoveryPlan(userId: string, planData: any): Promise<RecoveryPlan | null> {
    const { data, error } = await supabase
      .from('recovery_plans')
      .insert({
        user_id: userId,
        data: planData
      })
      .select()
      .single()
    
    if (error) {
      console.error('Error saving recovery plan:', error)
      return null
    }
    
    return data
  },

  async getLatestRecoveryPlan(userId: string): Promise<RecoveryPlan | null> {
    const { data, error } = await supabase
      .from('recovery_plans')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(1)
      .single()
    
    if (error && error.code !== 'PGRST116') {
      console.error('Error fetching recovery plan:', error)
      return null
    }
    
    return data
  }
}
