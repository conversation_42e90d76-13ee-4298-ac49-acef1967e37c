import { createClient } from '@supabase/supabase-js'

// Get credentials from environment variables
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables. Please check your .env.local file.')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database Types (based on your current localStorage data)
export interface User {
  id: string
  email: string
  created_at: string
  updated_at: string
}

export interface UserProfile {
  id: string
  user_id: string
  name: string
  age: number
  gender: 'Male' | 'Female'
  height: {
    unit: 'cm' | 'ft'
    value: number
    feet?: number
    inches?: number
  }
  weight: {
    unit: 'kg' | 'lbs'
    value: number
  }
  profile_picture?: string
  created_at: string
  updated_at: string
}

export interface Workout {
  id: string
  user_id: string
  name: string
  exercises: {
    exercise: string
    muscleGroup: string
  }[]
  created_at: string
  last_performed?: string
}

export interface ExerciseStats {
  id: string
  user_id: string
  exercise: string
  muscle_group: string
  max_weight: number
  max_reps: number
  created_at: string
  updated_at: string
}

// RecoveryPlan interface removed - functionality deprecated
