# Supabase Authentication Settings

## Disable Email Verification for Development

1. **Go to Supabase Dashboard** → Your Project
2. **Authentication** → Settings
3. **Email Auth** section
4. **Turn OFF "Enable email confirmations"**
5. **Save changes**

## For Production (Netlify)

When you deploy to Netlify:
1. **Keep email verification ON** for security
2. **Set Site URL** in Supabase Auth settings to your Netlify domain
3. **Add redirect URLs** for your production domain
4. **Email verification will work properly** with your custom domain

## Current Issue
- Email verification is ON but redirect URLs aren't configured properly
- This causes the confirmation flow to break
- Disabling for development will allow instant signup → profile → dashboard flow
