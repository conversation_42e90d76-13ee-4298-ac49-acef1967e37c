import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import NavigationHeader from './NavigationHeader';

interface SelectedExercise {
  muscleGroup: string;
  exercise: string;
}

const WorkoutBuilder: React.FC = () => {
  const navigate = useNavigate();
  const [workoutName, setWorkoutName] = useState('');
  const [selectedExercises, setSelectedExercises] = useState<SelectedExercise[]>([]);
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set());
  const [errors, setErrors] = useState<string[]>([]);

  const muscleGroups = {
    'Chest': ['Bench Press', 'Incline Press', 'Decline Press', 'Dumbbell Flyes', 'Push-ups', 'Chest Dips'],
    'Back': ['Pull-ups', 'Lat Pulldowns', 'Rows', 'Deadlifts', 'T-Bar Rows', 'Cable Rows'],
    'Shoulders': ['Overhead Press', 'Lateral Raises', 'Front Raises', 'Rear Delt Flyes', 'Shrugs', 'Arnold Press'],
    'Arms': ['Bicep Curls', 'Tricep Dips', 'Hammer Curls', 'Tricep Extensions', 'Preacher Curls', 'Close-Grip Press'],
    'Legs': ['Squats', 'Lunges', 'Leg Press', 'Calf Raises', 'Leg Curls', 'Leg Extensions'],
    'Core': ['Planks', 'Crunches', 'Russian Twists', 'Mountain Climbers', 'Dead Bug', 'Bicycle Crunches']
  };

  useEffect(() => {
    // Check if user has profile data
    const userProfile = localStorage.getItem('userProfileData');
    if (!userProfile) {
      navigate('/');
    }
  }, [navigate]);

  const toggleMuscleGroup = (group: string) => {
    const newExpanded = new Set(expandedGroups);
    if (newExpanded.has(group)) {
      newExpanded.delete(group);
    } else {
      newExpanded.add(group);
    }
    setExpandedGroups(newExpanded);
  };

  const toggleExercise = (muscleGroup: string, exercise: string) => {
    const isSelected = selectedExercises.some(e => e.muscleGroup === muscleGroup && e.exercise === exercise);
    
    if (isSelected) {
      setSelectedExercises(prev => prev.filter(e => !(e.muscleGroup === muscleGroup && e.exercise === exercise)));
    } else {
      setSelectedExercises(prev => [...prev, { muscleGroup, exercise }]);
    }
  };

  const isExerciseSelected = (muscleGroup: string, exercise: string) => {
    return selectedExercises.some(e => e.muscleGroup === muscleGroup && e.exercise === exercise);
  };

  const getSelectedExercisesForGroup = (group: string) => {
    return selectedExercises.filter(e => e.muscleGroup === group);
  };

  const handleSaveWorkout = () => {
    const validationErrors = [];
    if (!workoutName.trim()) validationErrors.push('Workout name is required');
    if (selectedExercises.length === 0) validationErrors.push('Please select at least one exercise');

    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      window.scrollTo({ top: 0, behavior: 'smooth' });
      return;
    }

    setErrors([]);

    // Create new workout
    const newWorkout = {
      id: Date.now().toString(),
      name: workoutName,
      exercises: selectedExercises,
      createdAt: new Date().toISOString()
    };

    // Save to localStorage
    const existingWorkouts = JSON.parse(localStorage.getItem('savedWorkouts') || '[]');
    const updatedWorkouts = [...existingWorkouts, newWorkout];
    localStorage.setItem('savedWorkouts', JSON.stringify(updatedWorkouts));

    console.log('=== NEW WORKOUT CREATED ===');
    console.log(JSON.stringify(newWorkout, null, 2));

    // Navigate back to dashboard
    navigate('/dashboard');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      <NavigationHeader />
      <div className="p-4 md:p-8 pb-24 md:pb-16">
        <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            🏗️ <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400">Workout Builder</span>
          </h1>
          <p className="text-purple-200 text-lg">
            Create your custom workout by selecting exercises from each muscle group
          </p>
        </div>

        <div className="bg-white/10 backdrop-blur-lg rounded-3xl shadow-2xl border border-white/20 p-8">
          {/* Error Display */}
          {errors.length > 0 && (
            <div className="mb-6 p-4 bg-red-500/20 border border-red-500/30 rounded-2xl backdrop-blur-sm">
              <div className="flex items-center space-x-2 mb-2">
                <span className="text-red-400 text-xl">⚠️</span>
                <h3 className="text-red-300 font-semibold">Please fix the following errors:</h3>
              </div>
              <ul className="list-disc list-inside space-y-1 text-red-200">
                {errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Workout Name */}
          <div className="mb-8">
            <label htmlFor="workoutName" className="block text-lg font-semibold text-purple-200 mb-3">
              Workout Name
            </label>
            <input
              type="text"
              id="workoutName"
              value={workoutName}
              onChange={(e) => setWorkoutName(e.target.value)}
              placeholder="e.g., Upper Body Blast, Leg Day, Full Body"
              className="w-full px-4 py-4 bg-white/10 border border-white/20 rounded-2xl shadow-lg backdrop-blur-sm text-white placeholder-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300 hover:bg-white/15 text-lg"
            />
          </div>

          {/* Selected Exercises Summary */}
          {selectedExercises.length > 0 && (
            <div className="mb-8 p-4 bg-green-500/20 border border-green-500/30 rounded-2xl backdrop-blur-sm">
              <h3 className="text-green-300 font-semibold mb-2">Selected Exercises ({selectedExercises.length})</h3>
              <div className="flex flex-wrap gap-2">
                {selectedExercises.map((exercise, index) => (
                  <span key={index} className="px-3 py-1 bg-green-500/20 text-green-300 text-sm rounded-full border border-green-500/30">
                    {exercise.exercise}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Exercise Selection */}
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-white mb-4">Select Exercises</h2>
            
            {Object.entries(muscleGroups).map(([group, exercises]) => {
              const selectedInGroup = getSelectedExercisesForGroup(group);
              
              return (
                <div key={group} className="bg-white/5 rounded-2xl border border-white/10 overflow-hidden">
                  <button
                    onClick={() => toggleMuscleGroup(group)}
                    className="w-full p-6 text-left flex items-center justify-between hover:bg-white/10 transition-all duration-300"
                  >
                    <div className="flex items-center space-x-4">
                      <span className="text-2xl">
                        {group === 'Chest' && '💪'}
                        {group === 'Back' && '🏋️'}
                        {group === 'Shoulders' && '🤸'}
                        {group === 'Arms' && '💪'}
                        {group === 'Legs' && '🦵'}
                        {group === 'Core' && '🔥'}
                      </span>
                      <div className="flex items-center space-x-3">
                        <h3 className="text-xl font-semibold text-white">{group}</h3>
                        {selectedInGroup.length > 0 && (
                          <span className="px-2 py-1 bg-purple-500/20 text-purple-300 text-sm rounded-full border border-purple-500/30">
                            {selectedInGroup.length} selected
                          </span>
                        )}
                      </div>
                    </div>
                    <span className={`text-white transition-transform duration-300 ${expandedGroups.has(group) ? 'rotate-180' : ''}`}>
                      ▼
                    </span>
                  </button>

                  {expandedGroups.has(group) && (
                    <div className="p-6 pt-0">
                      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-3">
                        {exercises.map(exercise => {
                          const isSelected = isExerciseSelected(group, exercise);
                          
                          return (
                            <button
                              key={exercise}
                              onClick={() => toggleExercise(group, exercise)}
                              className={`p-3 rounded-xl border transition-all duration-300 text-left ${
                                isSelected
                                  ? 'bg-purple-500/20 border-purple-500/50 text-purple-200'
                                  : 'bg-white/5 border-white/10 text-purple-300 hover:bg-white/10 hover:border-white/20'
                              }`}
                            >
                              <div className="flex items-center space-x-2">
                                <span className={`w-4 h-4 rounded border-2 flex items-center justify-center ${
                                  isSelected ? 'bg-purple-500 border-purple-500' : 'border-purple-400'
                                }`}>
                                  {isSelected && <span className="text-white text-xs">✓</span>}
                                </span>
                                <span className="font-medium">{exercise}</span>
                              </div>
                            </button>
                          );
                        })}
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          {/* Action Buttons */}
          <div className="mt-8 flex justify-center">
            <button
              onClick={handleSaveWorkout}
              className="bg-gradient-to-r from-purple-600 to-pink-600 text-white py-4 px-12 rounded-2xl hover:from-purple-700 hover:to-pink-700 focus:outline-none focus:ring-4 focus:ring-purple-500/50 transition-all duration-300 font-semibold text-lg shadow-xl shadow-purple-500/25 hover:shadow-2xl hover:shadow-purple-500/40 transform hover:scale-[1.02] active:scale-[0.98]"
            >
              💾 Save Workout
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  );
};

export default WorkoutBuilder;
