-- COMPREHENSIVE DATABASE FIX
-- Run this in Supabase SQL Editor to fix ALL issues

-- 1. TEMPORARILY DISABLE RLS to test (we'll re-enable after fixing)
ALTER TABLE user_profiles DISABLE ROW LEVEL SECURITY;

-- 2. Drop existing policies
DROP POLICY IF EXISTS "Users can view own profile" ON user_profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON user_profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON user_profiles;
DROP POLICY IF EXISTS "Users can delete own profile" ON user_profiles;

-- 3. Check current table structure and constraints
-- SELECT * FROM user_profiles LIMIT 1;

-- 4. Fix gender constraint to be more flexible
ALTER TABLE user_profiles DROP CONSTRAINT IF EXISTS user_profiles_gender_check;
ALTER TABLE user_profiles ADD CONSTRAINT user_profiles_gender_check
  CHECK (gender IN ('Male', 'Female', 'male', 'female'));

-- 5. Recreate RLS policies with better permissions
CREATE POLICY "Enable all for authenticated users" ON user_profiles
  FOR ALL USING (auth.role() = 'authenticated');

-- 6. Re-enable RLS
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- 7. Test queries (uncomment to test)
-- INSERT INTO user_profiles (user_id, name, age, gender) VALUES (auth.uid(), 'Test User', 25, 'Male');
-- SELECT * FROM user_profiles WHERE user_id = auth.uid();

-- 8. Grant necessary permissions
GRANT ALL ON user_profiles TO authenticated;
GRANT ALL ON workouts TO authenticated;
GRANT ALL ON exercise_stats TO authenticated;
