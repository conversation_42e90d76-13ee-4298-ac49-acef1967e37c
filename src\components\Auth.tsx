import React, { useState } from 'react'
import { useAuth } from '../contexts/AuthContext'

export const Auth: React.FC = () => {
  const [isSignUp, setIsSignUp] = useState(false)
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')

  const { signUp, signIn } = useAuth()

  // Email validation
  const isValidEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  // Password validation
  const isValidPassword = (password: string) => {
    return password.length >= 6
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setMessage('')

    // Validation
    if (!email.trim()) {
      setMessage('Email is required')
      setLoading(false)
      return
    }

    if (!isValidEmail(email)) {
      setMessage('Please enter a valid email address')
      setLoading(false)
      return
    }

    if (!password) {
      setMessage('Password is required')
      setLoading(false)
      return
    }

    if (!isValidPassword(password)) {
      setMessage('Password must be at least 6 characters long')
      setLoading(false)
      return
    }

    if (isSignUp) {
      if (!confirmPassword) {
        setMessage('Please confirm your password')
        setLoading(false)
        return
      }

      if (password !== confirmPassword) {
        setMessage('Passwords do not match')
        setLoading(false)
        return
      }
    }

    try {
      if (isSignUp) {
        console.log('🔐 Attempting signup for:', email)
        const { error } = await signUp(email, password)
        if (error) {
          console.error('❌ Signup error:', error)
          if (error.message.includes('already registered') || error.message.includes('already been registered')) {
            setMessage('This email is already registered. Please try logging in instead.')
          } else {
            setMessage(error.message)
          }
        } else {
          console.log('✅ Signup successful!')
          setMessage('Account created successfully! Setting up your profile...')
        }
      } else {
        console.log('🔐 Attempting login for:', email)
        const { error } = await signIn(email, password)
        if (error) {
          console.error('❌ Login error:', error)
          if (error.message.includes('Invalid login credentials')) {
            setMessage('Invalid email or password. Please check your credentials and try again.')
          } else {
            setMessage(error.message)
          }
        } else {
          console.log('✅ Login successful!')
        }
      }
    } catch (error: any) {
      console.error('❌ Unexpected auth error:', error)
      setMessage('An unexpected error occurred. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4">
      <div className="bg-white/10 backdrop-blur-lg rounded-3xl shadow-2xl border border-white/20 p-8 w-full max-w-md">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-2">
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400">
              MuscleMemory
            </span>
          </h1>
          <p className="text-purple-200">
            {isSignUp ? 'Create your account' : 'Welcome back!'}
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-purple-300 mb-2">
              Email
            </label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-400"
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-purple-300 mb-2">
              Password
            </label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              minLength={6}
              className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-400"
              placeholder="••••••••"
            />
          </div>

          {isSignUp && (
            <div>
              <label className="block text-sm font-medium text-purple-300 mb-2">
                Confirm Password
              </label>
              <input
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                required
                minLength={6}
                className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-400"
                placeholder="••••••••"
              />
            </div>
          )}

          {message && (
            <div className={`p-3 rounded-xl text-sm ${
              message.includes('Check your email') 
                ? 'bg-green-500/20 text-green-300 border border-green-500/30'
                : 'bg-red-500/20 text-red-300 border border-red-500/30'
            }`}>
              {message}
            </div>
          )}

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-gradient-to-r from-purple-600 to-pink-600 text-white font-semibold py-3 px-6 rounded-xl hover:from-purple-700 hover:to-pink-700 transition-all duration-200 disabled:opacity-50"
          >
            {loading ? 'Loading...' : (isSignUp ? 'Sign Up' : 'Sign In')}
          </button>
        </form>

        <div className="mt-6 text-center">
          <button
            onClick={() => setIsSignUp(!isSignUp)}
            className="text-purple-300 hover:text-white transition-colors"
          >
            {isSignUp 
              ? 'Already have an account? Sign in' 
              : "Don't have an account? Sign up"
            }
          </button>
        </div>
      </div>
    </div>
  )
}
