# 🚀 COMPLETE FIX - Do These Steps in Order

## STEP 1: Fix Supabase Database (CRITICAL)
1. **Go to Supabase Dashboard** → SQL Editor
2. **Copy ALL contents** of `fix-rls-policies.sql`
3. **<PERSON><PERSON> and RUN** the SQL script
4. **Should see**: "Success. No rows returned" or similar

## STEP 2: Disable Email Verification (DEVELOPMENT)
1. **Supabase Dashboard** → Authentication → Settings
2. **Find "Enable email confirmations"**
3. **TURN IT OFF** (toggle to disabled)
4. **Save changes**

## STEP 3: Test Complete Flow
1. **Click "🚪 Logout (Test)" button** (if you see profile form)
2. **Should see**: Login/Signup page
3. **Sign up with NEW email** (no confirmation needed now)
4. **Should immediately**: Go to profile setup form
5. **Fill out form** and submit
6. **Should see**: Dashboard
7. **Check Supabase** → Table Editor → user_profiles (should have data!)

## EXPECTED RESULTS
✅ No more 406 errors
✅ No email verification required
✅ Signup → Profile Setup → Dashboard
✅ Login → Dashboard (if profile exists)
✅ Data saves to user_profiles table

## If Still Issues
- Check browser console for specific errors
- Verify SQL script ran successfully
- Make sure email verification is OFF
- Try with completely new email address

## For Production Later
- Turn email verification back ON
- Set proper redirect URLs in Supabase Auth settings
- Configure custom domain for email links
