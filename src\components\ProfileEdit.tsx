import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import NavigationHeader from './NavigationHeader';

interface FormData {
  name: string;
  age: number | '';
  gender: string;
  heightUnit: 'cm' | 'ft';
  heightCm: number | '';
  heightFt: number | '';
  heightIn: number | '';
  weightUnit: 'kg' | 'lbs';
  weightKg: number | '';
  weightLbs: number | '';
  profilePicture?: string;
}

const ProfileEdit: React.FC = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState<FormData>({
    name: '',
    age: '',
    gender: '',
    heightUnit: 'cm',
    heightCm: '',
    heightFt: '',
    heightIn: '',
    weightUnit: 'kg',
    weightKg: '',
    weightLbs: '',
    profilePicture: ''
  });
  const [errors, setErrors] = useState<string[]>([]);



  useEffect(() => {
    // Load existing profile data
    const savedProfileData = localStorage.getItem('userProfileData');
    if (savedProfileData) {
      const profileData = JSON.parse(savedProfileData);
      
      // Convert profile data back to form format
      const formDataFromProfile: FormData = {
        name: profileData.name || '',
        age: profileData.age || '',
        gender: profileData.gender || '',
        heightUnit: profileData.height?.unit === 'cm' ? 'cm' : 'ft',
        heightCm: profileData.height?.unit === 'cm' ? profileData.height.value : '',
        heightFt: profileData.height?.feet || '',
        heightIn: profileData.height?.inches || '',
        weightUnit: profileData.weight?.unit || 'kg',
        weightKg: profileData.weight?.unit === 'kg' ? profileData.weight.value : '',
        weightLbs: profileData.weight?.unit === 'lbs' ? profileData.weight.value : '',

        profilePicture: profileData.profilePicture || ''
      };
      
      setFormData(formDataFromProfile);
    } else {
      navigate('/');
    }
  }, [navigate]);

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData(prev => {
      const newData = { ...prev, [field]: value };

      // Auto-convert height when switching units
      if (field === 'heightUnit') {
        if (value === 'cm' && prev.heightFt && prev.heightIn !== '') {
          // Convert ft/in to cm
          const totalInches = Number(prev.heightFt) * 12 + Number(prev.heightIn);
          const cm = Math.round(totalInches * 2.54);
          newData.heightCm = cm;
          newData.heightFt = '';
          newData.heightIn = '';
        } else if (value === 'ft' && prev.heightCm) {
          // Convert cm to ft/in
          const totalInches = Math.round(Number(prev.heightCm) / 2.54);
          const feet = Math.floor(totalInches / 12);
          const inches = totalInches % 12;
          newData.heightFt = feet;
          newData.heightIn = inches;
          newData.heightCm = '';
        }
      }

      // Auto-convert weight when switching units
      if (field === 'weightUnit') {
        if (value === 'kg' && prev.weightLbs) {
          // Convert lbs to kg
          const kg = Math.round(Number(prev.weightLbs) / 2.205);
          newData.weightKg = kg;
          newData.weightLbs = '';
        } else if (value === 'lbs' && prev.weightKg) {
          // Convert kg to lbs
          const lbs = Math.round(Number(prev.weightKg) * 2.205);
          newData.weightLbs = lbs;
          newData.weightKg = '';
        }
      }

      return newData;
    });
  };



  const handleProfilePictureUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setFormData(prev => ({ ...prev, profilePicture: result }));
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const validationErrors = [];
    if (!formData.name.trim()) validationErrors.push('Name is required');
    if (!formData.age || formData.age <= 0) validationErrors.push('Valid age is required');
    if (!formData.gender) validationErrors.push('Gender is required');
    
    // Height validation
    if (formData.heightUnit === 'cm') {
      if (!formData.heightCm || formData.heightCm <= 0) validationErrors.push('Valid height in cm is required');
    } else {
      if (!formData.heightFt || formData.heightFt <= 0) validationErrors.push('Valid height in feet is required');
      if (formData.heightIn === '' || formData.heightIn < 0 || formData.heightIn > 11) validationErrors.push('Valid inches (0-11) is required');
    }
    
    // Weight validation
    if (formData.weightUnit === 'kg') {
      if (!formData.weightKg || formData.weightKg <= 0) validationErrors.push('Valid weight in kg is required');
    } else {
      if (!formData.weightLbs || formData.weightLbs <= 0) validationErrors.push('Valid weight in lbs is required');
    }
    


    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      window.scrollTo({ top: 0, behavior: 'smooth' });
      return;
    }

    setErrors([]);

    // Process and save the data
    const processedData = {
      name: formData.name,
      age: Number(formData.age),
      gender: formData.gender,
      height: formData.heightUnit === 'cm' 
        ? { value: Number(formData.heightCm), unit: 'cm' }
        : { 
            value: Number(formData.heightFt) * 12 + Number(formData.heightIn), 
            unit: 'inches',
            feet: Number(formData.heightFt),
            inches: Number(formData.heightIn)
          },
      weight: formData.weightUnit === 'kg'
        ? { value: Number(formData.weightKg), unit: 'kg' }
        : { value: Number(formData.weightLbs), unit: 'lbs' },
      profilePicture: formData.profilePicture,
      submittedAt: new Date().toISOString()
    };

    // Save to localStorage
    localStorage.setItem('userProfileData', JSON.stringify(processedData));
    localStorage.setItem('userFormData', JSON.stringify(formData));

    console.log('=== UPDATED PROFILE DATA ===');
    console.log(JSON.stringify(processedData, null, 2));

    // Navigate back to dashboard
    navigate('/dashboard');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      <NavigationHeader />
      <div className="p-4 md:p-8 pb-24 md:pb-16">
        <div className="max-w-4xl mx-auto">
        <div className="bg-white/10 backdrop-blur-lg rounded-3xl shadow-2xl border border-white/20 p-8 md:p-12">
          <div className="text-center mb-12">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl mb-6">
              <span className="text-3xl">✏️</span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-white to-purple-200 bg-clip-text text-transparent mb-4">
              Edit Your Profile
            </h1>
            <p className="text-purple-200 text-lg max-w-2xl mx-auto">
              Update your information to keep your fitness recommendations accurate
            </p>
          </div>

          {/* Error Display */}
          {errors.length > 0 && (
            <div className="mb-6 p-4 bg-red-500/20 border border-red-500/30 rounded-2xl backdrop-blur-sm">
              <div className="flex items-center space-x-2 mb-2">
                <span className="text-red-400 text-xl">⚠️</span>
                <h3 className="text-red-300 font-semibold">Please fix the following errors:</h3>
              </div>
              <ul className="list-disc list-inside space-y-1 text-red-200">
                {errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          )}

          <form className="space-y-8" onSubmit={handleSubmit}>
            {/* Profile Picture */}
            <div className="text-center">
              <div className="inline-block relative">
                <div className="w-32 h-32 rounded-full overflow-hidden bg-white/10 border-4 border-white/20 mx-auto mb-4">
                  {formData.profilePicture ? (
                    <img 
                      src={formData.profilePicture} 
                      alt="Profile" 
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-6xl text-purple-300">
                      👤
                    </div>
                  )}
                </div>
                <label className="absolute bottom-0 right-0 bg-purple-600 hover:bg-purple-700 text-white p-2 rounded-full cursor-pointer transition-colors">
                  <span className="text-lg">📷</span>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleProfilePictureUpload}
                    className="hidden"
                  />
                </label>
              </div>
              <p className="text-purple-300 text-sm">Click the camera icon to upload a profile picture</p>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              {/* Name */}
              <div className="space-y-2">
                <label htmlFor="name" className="block text-sm font-semibold text-purple-200 mb-3">
                  What's your name?
                </label>
                <input
                  type="text"
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className="w-full px-4 py-4 bg-white/10 border border-white/20 rounded-2xl shadow-lg backdrop-blur-sm text-white placeholder-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300 hover:bg-white/15 text-lg"
                  placeholder="Enter your name"
                />
              </div>

              {/* Age */}
              <div className="space-y-2">
                <label htmlFor="age" className="block text-sm font-semibold text-purple-200 mb-3">
                  How old are you?
                </label>
                <input
                  type="number"
                  id="age"
                  value={formData.age}
                  onChange={(e) => handleInputChange('age', Number(e.target.value))}
                  className="w-full px-4 py-4 bg-white/10 border border-white/20 rounded-2xl shadow-lg backdrop-blur-sm text-white placeholder-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300 hover:bg-white/15 text-lg"
                  placeholder="Enter your age"
                  min="1"
                />
              </div>
            </div>

            {/* Gender */}
            <div className="space-y-4">
              <label className="block text-sm font-semibold text-purple-200 mb-3">
                What's your gender?
              </label>
              <div className="grid grid-cols-2 gap-4">
                {['Male', 'Female'].map(option => (
                  <button
                    key={option}
                    type="button"
                    onClick={() => handleInputChange('gender', option)}
                    className={`p-4 rounded-2xl border-2 transition-all duration-300 text-lg font-medium ${
                      formData.gender === option
                        ? 'bg-purple-500/20 border-purple-500 text-purple-200'
                        : 'bg-white/5 border-white/20 text-purple-300 hover:bg-white/10 hover:border-white/30'
                    }`}
                  >
                    {option}
                  </button>
                ))}
              </div>
            </div>

            {/* Height */}
            <div className="space-y-4">
              <label className="block text-sm font-semibold text-purple-200 mb-3">
                What's your height?
              </label>
              <div className="grid grid-cols-2 gap-4 mb-4">
                {[
                  { value: 'cm', label: 'Centimeters' },
                  { value: 'ft', label: 'Feet & Inches' }
                ].map(option => (
                  <button
                    key={option.value}
                    type="button"
                    onClick={() => handleInputChange('heightUnit', option.value as 'cm' | 'ft')}
                    className={`p-4 rounded-2xl border-2 transition-all duration-300 text-lg font-medium ${
                      formData.heightUnit === option.value
                        ? 'bg-purple-500/20 border-purple-500 text-purple-200'
                        : 'bg-white/5 border-white/20 text-purple-300 hover:bg-white/10 hover:border-white/30'
                    }`}
                  >
                    {option.label}
                  </button>
                ))}
              </div>
              
              {formData.heightUnit === 'cm' ? (
                <div>
                  <input
                    type="number"
                    value={formData.heightCm}
                    onChange={(e) => handleInputChange('heightCm', Number(e.target.value))}
                    className="w-full px-4 py-4 bg-white/10 border border-white/20 rounded-2xl shadow-lg backdrop-blur-sm text-white placeholder-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300 hover:bg-white/15 text-lg"
                    placeholder="Height in cm (e.g., 175)"
                    min="1"
                  />
                  {formData.heightCm && (
                    <p className="text-purple-300 text-sm mt-2 text-center">
                      ≈ {Math.floor((formData.heightCm / 2.54) / 12)}'{Math.round((formData.heightCm / 2.54) % 12)}"
                    </p>
                  )}
                </div>
              ) : (
                <div>
                  <div className="grid grid-cols-2 gap-4">
                    <input
                      type="number"
                      value={formData.heightFt}
                      onChange={(e) => handleInputChange('heightFt', Number(e.target.value))}
                      className="w-full px-4 py-4 bg-white/10 border border-white/20 rounded-2xl shadow-lg backdrop-blur-sm text-white placeholder-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300 hover:bg-white/15 text-lg"
                      placeholder="Feet (e.g., 5)"
                      min="1"
                    />
                    <input
                      type="number"
                      value={formData.heightIn}
                      onChange={(e) => handleInputChange('heightIn', Number(e.target.value))}
                      className="w-full px-4 py-4 bg-white/10 border border-white/20 rounded-2xl shadow-lg backdrop-blur-sm text-white placeholder-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300 hover:bg-white/15 text-lg"
                      placeholder="Inches (e.g., 8)"
                      min="0"
                      max="11"
                    />
                  </div>
                  {formData.heightFt && formData.heightIn !== '' && (
                    <p className="text-purple-300 text-sm mt-2 text-center">
                      ≈ {Math.round((Number(formData.heightFt) * 12 + Number(formData.heightIn)) * 2.54)} cm
                    </p>
                  )}
                </div>
              )}
            </div>

            {/* Weight */}
            <div className="space-y-4">
              <label className="block text-sm font-semibold text-purple-200 mb-3">
                What's your weight?
              </label>
              <div className="grid grid-cols-2 gap-4 mb-4">
                {[
                  { value: 'kg', label: 'Kilograms' },
                  { value: 'lbs', label: 'Pounds' }
                ].map(option => (
                  <button
                    key={option.value}
                    type="button"
                    onClick={() => handleInputChange('weightUnit', option.value as 'kg' | 'lbs')}
                    className={`p-4 rounded-2xl border-2 transition-all duration-300 text-lg font-medium ${
                      formData.weightUnit === option.value
                        ? 'bg-purple-500/20 border-purple-500 text-purple-200'
                        : 'bg-white/5 border-white/20 text-purple-300 hover:bg-white/10 hover:border-white/30'
                    }`}
                  >
                    {option.label}
                  </button>
                ))}
              </div>
              
              {formData.weightUnit === 'kg' ? (
                <div>
                  <input
                    type="number"
                    value={formData.weightKg}
                    onChange={(e) => handleInputChange('weightKg', Number(e.target.value))}
                    className="w-full px-4 py-4 bg-white/10 border border-white/20 rounded-2xl shadow-lg backdrop-blur-sm text-white placeholder-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300 hover:bg-white/15 text-lg"
                    placeholder="Weight in kg (e.g., 70)"
                    min="1"
                  />
                  {formData.weightKg && (
                    <p className="text-purple-300 text-sm mt-2 text-center">
                      ≈ {Math.round(Number(formData.weightKg) * 2.205)} lbs
                    </p>
                  )}
                </div>
              ) : (
                <div>
                  <input
                    type="number"
                    value={formData.weightLbs}
                    onChange={(e) => handleInputChange('weightLbs', Number(e.target.value))}
                    className="w-full px-4 py-4 bg-white/10 border border-white/20 rounded-2xl shadow-lg backdrop-blur-sm text-white placeholder-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300 hover:bg-white/15 text-lg"
                    placeholder="Weight in lbs (e.g., 154)"
                    min="1"
                  />
                  {formData.weightLbs && (
                    <p className="text-purple-300 text-sm mt-2 text-center">
                      ≈ {Math.round(Number(formData.weightLbs) / 2.205)} kg
                    </p>
                  )}
                </div>
              )}
            </div>



            {/* Action Buttons */}
            <div className="flex gap-4 pt-8">
              <button
                type="button"
                onClick={() => navigate('/dashboard')}
                className="flex-1 bg-white/10 text-white py-4 px-8 rounded-2xl hover:bg-white/20 focus:outline-none focus:ring-4 focus:ring-white/25 transition-all duration-300 font-semibold text-lg border border-white/20"
              >
                ← Cancel
              </button>
              <button
                type="submit"
                className="flex-1 bg-gradient-to-r from-purple-600 to-pink-600 text-white py-4 px-8 rounded-2xl hover:from-purple-700 hover:to-pink-700 focus:outline-none focus:ring-4 focus:ring-purple-500/50 transition-all duration-300 font-semibold text-lg shadow-xl shadow-purple-500/25 hover:shadow-2xl hover:shadow-purple-500/40 transform hover:scale-[1.02] active:scale-[0.98]"
              >
                💾 Save Changes
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
  );
};

export default ProfileEdit;
