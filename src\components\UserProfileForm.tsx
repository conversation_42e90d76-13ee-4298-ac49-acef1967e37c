import React, { useState } from 'react';

interface FormData {
  name: string;
  age: number | '';
  gender: string;
  heightUnit: 'cm' | 'ft';
  heightCm: number | '';
  heightFt: number | '';
  heightIn: number | '';
  weightUnit: 'kg' | 'lbs';
  weightKg: number | '';
  weightLbs: number | '';
  lastWorkoutDate: string;
  workoutTypes: string[];
  goals: string[];
}

const UserProfileForm: React.FC = () => {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    age: '',
    gender: '',
    heightUnit: 'cm',
    heightCm: '',
    heightFt: '',
    heightIn: '',
    weightUnit: 'kg',
    weightKg: '',
    weightLbs: '',
    lastWorkoutDate: '',
    workoutTypes: [],
    goals: []
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'age' || name.includes('height') || name.includes('weight') 
        ? (value === '' ? '' : Number(value))
        : value
    }));
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>, field: 'workoutTypes' | 'goals') => {
    const { value, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [field]: checked 
        ? [...prev[field], value]
        : prev[field].filter(item => item !== value)
    }));
  };

  const workoutTypeOptions = ['Strength', 'Cardio', 'CrossFit', 'Yoga', 'Other'];
  const goalOptions = ['Regain strength', 'Lose weight', 'Build endurance', 'Maintain health'];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8 px-4">
      <div className="max-w-2xl mx-auto">
        <div className="bg-white rounded-xl shadow-lg p-8">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Welcome to ReGyminate</h1>
            <p className="text-gray-600">Let's get you back on track with your fitness journey!</p>
          </div>

          <form className="space-y-6">
            {/* Name */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                Name
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter your name"
              />
            </div>

            {/* Age */}
            <div>
              <label htmlFor="age" className="block text-sm font-medium text-gray-700 mb-2">
                Age
              </label>
              <input
                type="number"
                id="age"
                name="age"
                value={formData.age}
                onChange={handleInputChange}
                min="1"
                max="120"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter your age"
              />
            </div>

            {/* Gender */}
            <div>
              <label htmlFor="gender" className="block text-sm font-medium text-gray-700 mb-2">
                Gender
              </label>
              <select
                id="gender"
                name="gender"
                value={formData.gender}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select gender</option>
                <option value="male">Male</option>
                <option value="female">Female</option>
                <option value="other">Other</option>
                <option value="prefer-not-to-say">Prefer not to say</option>
              </select>
            </div>

            {/* Height */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Height</label>
              <div className="flex gap-3">
                <select
                  name="heightUnit"
                  value={formData.heightUnit}
                  onChange={handleInputChange}
                  className="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="cm">cm</option>
                  <option value="ft">ft/in</option>
                </select>
                
                {formData.heightUnit === 'cm' ? (
                  <input
                    type="number"
                    name="heightCm"
                    value={formData.heightCm}
                    onChange={handleInputChange}
                    placeholder="Height in cm"
                    min="1"
                    max="300"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                ) : (
                  <div className="flex-1 flex gap-2">
                    <input
                      type="number"
                      name="heightFt"
                      value={formData.heightFt}
                      onChange={handleInputChange}
                      placeholder="Feet"
                      min="1"
                      max="8"
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                    <input
                      type="number"
                      name="heightIn"
                      value={formData.heightIn}
                      onChange={handleInputChange}
                      placeholder="Inches"
                      min="0"
                      max="11"
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                )}
              </div>
            </div>

            {/* Weight */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Weight</label>
              <div className="flex gap-3">
                <select
                  name="weightUnit"
                  value={formData.weightUnit}
                  onChange={handleInputChange}
                  className="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="kg">kg</option>
                  <option value="lbs">lbs</option>
                </select>
                
                <input
                  type="number"
                  name={formData.weightUnit === 'kg' ? 'weightKg' : 'weightLbs'}
                  value={formData.weightUnit === 'kg' ? formData.weightKg : formData.weightLbs}
                  onChange={handleInputChange}
                  placeholder={`Weight in ${formData.weightUnit}`}
                  min="1"
                  max={formData.weightUnit === 'kg' ? "500" : "1100"}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            {/* Last Workout Date */}
            <div>
              <label htmlFor="lastWorkoutDate" className="block text-sm font-medium text-gray-700 mb-2">
                Date of Last Workout
              </label>
              <input
                type="date"
                id="lastWorkoutDate"
                name="lastWorkoutDate"
                value={formData.lastWorkoutDate}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Previous Workout Types */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Previous Workout Types (select all that apply)
              </label>
              <div className="grid grid-cols-2 gap-3">
                {workoutTypeOptions.map((type) => (
                  <label key={type} className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      value={type}
                      checked={formData.workoutTypes.includes(type)}
                      onChange={(e) => handleCheckboxChange(e, 'workoutTypes')}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">{type}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Goals */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Goals for Returning (select all that apply)
              </label>
              <div className="grid grid-cols-1 gap-3">
                {goalOptions.map((goal) => (
                  <label key={goal} className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      value={goal}
                      checked={formData.goals.includes(goal)}
                      onChange={(e) => handleCheckboxChange(e, 'goals')}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">{goal}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Submit Button */}
            <div className="pt-6">
              <button
                type="button"
                onClick={() => console.log('Form Data:', formData)}
                className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-200 font-medium"
              >
                Get My Personalized Plan
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default UserProfileForm;
