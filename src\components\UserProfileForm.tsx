import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { profileService } from '../services/database';
import type { UserProfile } from '../lib/supabase';

interface FormData {
  name: string;
  age: number | '';
  gender: string;
  heightUnit: 'cm' | 'ft';
  heightCm: number | '';
  heightFt: number | '';
  heightIn: number | '';
  weightUnit: 'kg' | 'lbs';
  weightKg: number | '';
  weightLbs: number | '';
}

const UserProfileForm: React.FC = () => {
  const navigate = useNavigate();
  const { user, signOut } = useAuth();
  const [errors, setErrors] = useState<string[]>([]);
  const [formData, setFormData] = useState<FormData>({
    name: '',
    age: '',
    gender: '',
    heightUnit: 'cm',
    heightCm: '',
    heightFt: '',
    heightIn: '',
    weightUnit: 'kg',
    weightKg: '',
    weightLbs: ''
  });

  // Fresh form for new users - no localStorage persistence needed

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) {
      setErrors(['Authentication error. Please try logging in again.']);
      return;
    }

    // Validation
    const validationErrors = [];
    if (!formData.name.trim()) validationErrors.push('Name is required');
    if (!formData.age || formData.age <= 0) validationErrors.push('Valid age is required');
    if (!formData.gender) validationErrors.push('Gender is required');

    // Height validation
    if (formData.heightUnit === 'cm') {
      if (!formData.heightCm || formData.heightCm <= 0) validationErrors.push('Height in cm is required');
    } else {
      if (!formData.heightFt || formData.heightFt <= 0) validationErrors.push('Height in feet is required');
      if (formData.heightIn === '' || formData.heightIn < 0 || formData.heightIn > 11) validationErrors.push('Valid inches (0-11) is required');
    }

    // Weight validation
    if (formData.weightUnit === 'kg') {
      if (!formData.weightKg || formData.weightKg <= 0) validationErrors.push('Weight in kg is required');
    } else {
      if (!formData.weightLbs || formData.weightLbs <= 0) validationErrors.push('Weight in lbs is required');
    }

    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      window.scrollTo({ top: 0, behavior: 'smooth' });
      return;
    }

    setErrors([]);

    try {
      // Create profile data for database
      const profileData: Omit<UserProfile, "id" | "created_at" | "updated_at"> = {
        user_id: user.id,
        name: formData.name,
        age: typeof formData.age === 'string' ? parseInt(formData.age) : formData.age,
        gender: (formData.gender === 'male' ? 'Male' : formData.gender === 'female' ? 'Female' : formData.gender) as 'Male' | 'Female',
        height: formData.heightUnit === 'cm'
          ? { value: typeof formData.heightCm === 'string' ? parseInt(formData.heightCm) : formData.heightCm, unit: 'cm' }
          : {
              value: (typeof formData.heightFt === 'string' ? parseInt(formData.heightFt) : formData.heightFt) * 12 +
                     (typeof formData.heightIn === 'string' ? parseInt(formData.heightIn) : formData.heightIn),
              unit: 'ft',
              feet: typeof formData.heightFt === 'string' ? parseInt(formData.heightFt) : formData.heightFt,
              inches: typeof formData.heightIn === 'string' ? parseInt(formData.heightIn) : formData.heightIn
            },
        weight: formData.weightUnit === 'kg'
          ? { value: typeof formData.weightKg === 'string' ? parseInt(formData.weightKg) : formData.weightKg, unit: 'kg' }
          : { value: typeof formData.weightLbs === 'string' ? parseInt(formData.weightLbs) : formData.weightLbs, unit: 'lbs' },
        workout_types: [],
        goals: []
      };

      // Save to database
      console.log('💾 Attempting to save profile:', JSON.stringify(profileData, null, 2));
      await profileService.createProfile(profileData);

      console.log('✅ PROFILE SAVED TO DATABASE SUCCESSFULLY!');

      // Navigate to dashboard
      navigate('/dashboard');
    } catch (error) {
      console.error('Error saving profile:', error);
      setErrors(['Failed to save profile. Please try again.']);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    let newFormData = {
      ...formData,
      [name]: (name === 'age' || (name.includes('height') && name !== 'heightUnit') || (name.includes('weight') && name !== 'weightUnit'))
        ? (value === '' ? '' : Number(value))
        : value
    };

    // Auto-convert height when switching units
    if (name === 'heightUnit') {
      if (value === 'cm' && formData.heightFt && formData.heightIn !== '') {
        // Convert ft/in to cm
        const totalInches = Number(formData.heightFt) * 12 + Number(formData.heightIn);
        const cm = Math.round(totalInches * 2.54);
        newFormData = { ...newFormData, heightCm: cm, heightFt: '', heightIn: '' };
      } else if (value === 'ft' && formData.heightCm) {
        // Convert cm to ft/in
        const totalInches = Math.round(Number(formData.heightCm) / 2.54);
        const feet = Math.floor(totalInches / 12);
        const inches = totalInches % 12;
        newFormData = { ...newFormData, heightFt: feet, heightIn: inches, heightCm: '' };
      }
    }

    // Auto-convert weight when switching units
    if (name === 'weightUnit') {
      if (value === 'kg' && formData.weightLbs) {
        // Convert lbs to kg
        const kg = Math.round(Number(formData.weightLbs) / 2.205);
        newFormData = { ...newFormData, weightKg: kg, weightLbs: '' };
      } else if (value === 'lbs' && formData.weightKg) {
        // Convert kg to lbs
        const lbs = Math.round(Number(formData.weightKg) * 2.205);
        newFormData = { ...newFormData, weightLbs: lbs, weightKg: '' };
      }
    }

    setFormData(newFormData);
    // Save to localStorage whenever form data changes
    localStorage.setItem('userFormData', JSON.stringify(newFormData));
  };



  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 py-8 px-4 pb-24 md:pb-16">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white/10 backdrop-blur-lg rounded-3xl shadow-2xl border border-white/20 p-8 md:p-12">
          <div className="text-center mb-12">
            <div className="flex justify-between items-start mb-6">
              <div></div>
              <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl">
                <span className="text-3xl">💪</span>
              </div>
              <button
                onClick={() => signOut()}
                className="px-4 py-2 bg-red-500/20 text-red-300 rounded-lg hover:bg-red-500/30 transition-colors text-sm"
              >
                🚪 Logout (Test)
              </button>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-white to-purple-200 bg-clip-text text-transparent mb-4">
              Welcome to MuscleMemory
            </h1>
            <p className="text-purple-200 text-lg max-w-2xl mx-auto">
              Let's get you back on track with your fitness journey! Tell us about yourself to create your personalized workout plan.
            </p>
          </div>

          {/* Error Display */}
          {errors.length > 0 && (
            <div className="mb-6 p-4 bg-red-500/20 border border-red-500/30 rounded-2xl backdrop-blur-sm">
              <div className="flex items-center space-x-2 mb-2">
                <span className="text-red-400 text-xl">⚠️</span>
                <h3 className="text-red-300 font-semibold">Please fix the following errors:</h3>
              </div>
              <ul className="list-disc list-inside space-y-1 text-red-200">
                {errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          )}

          <form className="space-y-8" onSubmit={handleSubmit}>
            <div className="grid md:grid-cols-2 gap-8">
              {/* Name */}
              <div className="space-y-2">
                <label htmlFor="name" className="block text-sm font-semibold text-purple-200 mb-3">
                  What's your name?
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full px-4 py-4 bg-white/10 border border-white/20 rounded-2xl shadow-lg backdrop-blur-sm text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300 hover:bg-white/15"
                  placeholder="Enter your name"
                />
              </div>

              {/* Age */}
              <div className="space-y-2">
                <label htmlFor="age" className="block text-sm font-semibold text-purple-200 mb-3">
                  How old are you?
                </label>
                <input
                  type="number"
                  id="age"
                  name="age"
                  value={formData.age}
                  onChange={handleInputChange}
                  min="1"
                  max="120"
                  className="w-full px-4 py-4 bg-white/10 border border-white/20 rounded-2xl shadow-lg backdrop-blur-sm text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300 hover:bg-white/15"
                  placeholder="Enter your age"
                />
              </div>
            </div>

            {/* Gender */}
            <div className="space-y-2">
              <label htmlFor="gender" className="block text-sm font-semibold text-purple-200 mb-3">
                Gender
              </label>
              <select
                id="gender"
                name="gender"
                value={formData.gender}
                onChange={handleInputChange}
                className="w-full px-4 py-4 bg-white/10 border border-white/20 rounded-2xl shadow-lg backdrop-blur-sm text-white focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300 hover:bg-white/15"
              >
                <option value="" className="bg-slate-800 text-white">Select gender</option>
                <option value="male" className="bg-slate-800 text-white">Male</option>
                <option value="female" className="bg-slate-800 text-white">Female</option>
              </select>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              {/* Height */}
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-purple-200 mb-3">What's your height?</label>
                <div className="flex gap-3">
                  <select
                    name="heightUnit"
                    value={formData.heightUnit}
                    onChange={handleInputChange}
                    className="px-4 py-4 bg-white/10 border border-white/20 rounded-2xl shadow-lg backdrop-blur-sm text-white focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300 hover:bg-white/15"
                  >
                    <option value="cm" className="bg-slate-800 text-white">cm</option>
                    <option value="ft" className="bg-slate-800 text-white">ft/in</option>
                  </select>

                  {formData.heightUnit === 'cm' ? (
                    <input
                      type="number"
                      name="heightCm"
                      value={formData.heightCm}
                      onChange={handleInputChange}
                      placeholder="Height in cm"
                      min="1"
                      max="300"
                      className="flex-1 px-4 py-4 bg-white/10 border border-white/20 rounded-2xl shadow-lg backdrop-blur-sm text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300 hover:bg-white/15"
                    />
                  ) : (
                    <div className="flex-1 flex gap-2">
                      <input
                        type="number"
                        name="heightFt"
                        value={formData.heightFt}
                        onChange={handleInputChange}
                        placeholder="Feet"
                        min="1"
                        max="8"
                        className="flex-1 px-4 py-4 bg-white/10 border border-white/20 rounded-2xl shadow-lg backdrop-blur-sm text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300 hover:bg-white/15"
                      />
                      <input
                        type="number"
                        name="heightIn"
                        value={formData.heightIn}
                        onChange={handleInputChange}
                        placeholder="Inches"
                        min="0"
                        max="11"
                        className="flex-1 px-4 py-4 bg-white/10 border border-white/20 rounded-2xl shadow-lg backdrop-blur-sm text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300 hover:bg-white/15"
                      />
                    </div>
                  )}
                </div>
              </div>

              {/* Weight */}
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-purple-200 mb-3">What's your weight?</label>
                <div className="flex gap-3">
                  <select
                    name="weightUnit"
                    value={formData.weightUnit}
                    onChange={handleInputChange}
                    className="px-4 py-4 bg-white/10 border border-white/20 rounded-2xl shadow-lg backdrop-blur-sm text-white focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300 hover:bg-white/15"
                  >
                    <option value="kg" className="bg-slate-800 text-white">kg</option>
                    <option value="lbs" className="bg-slate-800 text-white">lbs</option>
                  </select>

                  <input
                    type="number"
                    name={formData.weightUnit === 'kg' ? 'weightKg' : 'weightLbs'}
                    value={formData.weightUnit === 'kg' ? formData.weightKg : formData.weightLbs}
                    onChange={handleInputChange}
                    placeholder={`Weight in ${formData.weightUnit}`}
                    min="1"
                    max={formData.weightUnit === 'kg' ? "500" : "1100"}
                    className="flex-1 px-4 py-4 bg-white/10 border border-white/20 rounded-2xl shadow-lg backdrop-blur-sm text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300 hover:bg-white/15"
                  />
                </div>
              </div>
            </div>





            {/* Submit Button */}
            <div className="pt-8">
              <button
                type="submit"
                className="w-full bg-gradient-to-r from-purple-600 to-pink-600 text-white py-4 px-8 rounded-2xl hover:from-purple-700 hover:to-pink-700 focus:outline-none focus:ring-4 focus:ring-purple-500/50 transition-all duration-300 font-semibold text-lg shadow-xl shadow-purple-500/25 hover:shadow-2xl hover:shadow-purple-500/40 transform hover:scale-[1.02] active:scale-[0.98]"
              >
                <span className="flex items-center justify-center space-x-2">
                  <span>🚀</span>
                  <span>Start My Fitness Journey</span>
                </span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default UserProfileForm;
