import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

interface FormData {
  name: string;
  age: number | '';
  gender: string;
  heightUnit: 'cm' | 'ft';
  heightCm: number | '';
  heightFt: number | '';
  heightIn: number | '';
  weightUnit: 'kg' | 'lbs';
  weightKg: number | '';
  weightLbs: number | '';
  lastWorkoutDate: string;
  workoutTypes: string[];
  goals: string[];
}

const UserProfileForm: React.FC = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState<FormData>({
    name: '',
    age: '',
    gender: '',
    heightUnit: 'cm',
    heightCm: '',
    heightFt: '',
    heightIn: '',
    weightUnit: 'kg',
    weightKg: '',
    weightLbs: '',
    lastWorkoutDate: '',
    workoutTypes: [],
    goals: []
  });

  // Load saved form data on component mount
  React.useEffect(() => {
    const savedFormData = localStorage.getItem('userFormData');
    if (savedFormData) {
      setFormData(JSON.parse(savedFormData));
    }
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Create a clean data object for submission
    const submissionData = {
      name: formData.name,
      age: typeof formData.age === 'string' ? parseInt(formData.age) : formData.age,
      gender: formData.gender,
      height: formData.heightUnit === 'cm'
        ? { value: typeof formData.heightCm === 'string' ? parseInt(formData.heightCm) : formData.heightCm, unit: 'cm' }
        : {
            feet: typeof formData.heightFt === 'string' ? parseInt(formData.heightFt) : formData.heightFt,
            inches: typeof formData.heightIn === 'string' ? parseInt(formData.heightIn) : formData.heightIn,
            unit: 'ft/in'
          },
      weight: formData.weightUnit === 'kg'
        ? { value: typeof formData.weightKg === 'string' ? parseInt(formData.weightKg) : formData.weightKg, unit: 'kg' }
        : { value: typeof formData.weightLbs === 'string' ? parseInt(formData.weightLbs) : formData.weightLbs, unit: 'lbs' },
      lastWorkoutDate: formData.lastWorkoutDate,
      workoutTypes: formData.workoutTypes,
      goals: formData.goals,
      submittedAt: new Date().toISOString()
    };

    // Log the data to console for testing/prototyping
    console.log('=== FORM SUBMISSION DATA ===');
    console.log(JSON.stringify(submissionData, null, 2));

    // Store the user data in localStorage for the next page
    localStorage.setItem('userProfileData', JSON.stringify(submissionData));

    // Navigate to workout selection page
    navigate('/workout-selection');
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    const newFormData = {
      ...formData,
      [name]: (name === 'age' || (name.includes('height') && name !== 'heightUnit') || (name.includes('weight') && name !== 'weightUnit'))
        ? (value === '' ? '' : Number(value))
        : value
    };
    setFormData(newFormData);
    // Save to localStorage whenever form data changes
    localStorage.setItem('userFormData', JSON.stringify(newFormData));
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>, field: 'workoutTypes' | 'goals') => {
    const { value, checked } = e.target;
    const newFormData = {
      ...formData,
      [field]: checked
        ? [...formData[field], value]
        : formData[field].filter(item => item !== value)
    };
    setFormData(newFormData);
    // Save to localStorage whenever form data changes
    localStorage.setItem('userFormData', JSON.stringify(newFormData));
  };

  const workoutTypeOptions = ['Strength', 'Cardio', 'CrossFit', 'Yoga', 'Other'];
  const goalOptions = ['Regain strength', 'Lose weight', 'Build endurance', 'Maintain health'];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 py-8 px-4 pb-24 md:pb-16">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white/10 backdrop-blur-lg rounded-3xl shadow-2xl border border-white/20 p-8 md:p-12">
          <div className="text-center mb-12">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl mb-6">
              <span className="text-3xl">💪</span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-white to-purple-200 bg-clip-text text-transparent mb-4">
              Welcome to MuscleMemory
            </h1>
            <p className="text-purple-200 text-lg max-w-2xl mx-auto">
              Let's get you back on track with your fitness journey! Tell us about yourself to create your personalized workout plan.
            </p>
          </div>

          <form className="space-y-8" onSubmit={handleSubmit}>
            <div className="grid md:grid-cols-2 gap-8">
              {/* Name */}
              <div className="space-y-2">
                <label htmlFor="name" className="block text-sm font-semibold text-purple-200 mb-3">
                  What's your name?
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full px-4 py-4 bg-white/10 border border-white/20 rounded-2xl shadow-lg backdrop-blur-sm text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300 hover:bg-white/15"
                  placeholder="Enter your name"
                />
              </div>

              {/* Age */}
              <div className="space-y-2">
                <label htmlFor="age" className="block text-sm font-semibold text-purple-200 mb-3">
                  How old are you?
                </label>
                <input
                  type="number"
                  id="age"
                  name="age"
                  value={formData.age}
                  onChange={handleInputChange}
                  min="1"
                  max="120"
                  className="w-full px-4 py-4 bg-white/10 border border-white/20 rounded-2xl shadow-lg backdrop-blur-sm text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300 hover:bg-white/15"
                  placeholder="Enter your age"
                />
              </div>
            </div>

            {/* Gender */}
            <div className="space-y-2">
              <label htmlFor="gender" className="block text-sm font-semibold text-purple-200 mb-3">
                Gender
              </label>
              <select
                id="gender"
                name="gender"
                value={formData.gender}
                onChange={handleInputChange}
                className="w-full px-4 py-4 bg-white/10 border border-white/20 rounded-2xl shadow-lg backdrop-blur-sm text-white focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300 hover:bg-white/15"
              >
                <option value="" className="bg-slate-800 text-white">Select gender</option>
                <option value="male" className="bg-slate-800 text-white">Male</option>
                <option value="female" className="bg-slate-800 text-white">Female</option>
              </select>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              {/* Height */}
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-purple-200 mb-3">What's your height?</label>
                <div className="flex gap-3">
                  <select
                    name="heightUnit"
                    value={formData.heightUnit}
                    onChange={handleInputChange}
                    className="px-4 py-4 bg-white/10 border border-white/20 rounded-2xl shadow-lg backdrop-blur-sm text-white focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300 hover:bg-white/15"
                  >
                    <option value="cm" className="bg-slate-800 text-white">cm</option>
                    <option value="ft" className="bg-slate-800 text-white">ft/in</option>
                  </select>

                  {formData.heightUnit === 'cm' ? (
                    <input
                      type="number"
                      name="heightCm"
                      value={formData.heightCm}
                      onChange={handleInputChange}
                      placeholder="Height in cm"
                      min="1"
                      max="300"
                      className="flex-1 px-4 py-4 bg-white/10 border border-white/20 rounded-2xl shadow-lg backdrop-blur-sm text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300 hover:bg-white/15"
                    />
                  ) : (
                    <div className="flex-1 flex gap-2">
                      <input
                        type="number"
                        name="heightFt"
                        value={formData.heightFt}
                        onChange={handleInputChange}
                        placeholder="Feet"
                        min="1"
                        max="8"
                        className="flex-1 px-4 py-4 bg-white/10 border border-white/20 rounded-2xl shadow-lg backdrop-blur-sm text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300 hover:bg-white/15"
                      />
                      <input
                        type="number"
                        name="heightIn"
                        value={formData.heightIn}
                        onChange={handleInputChange}
                        placeholder="Inches"
                        min="0"
                        max="11"
                        className="flex-1 px-4 py-4 bg-white/10 border border-white/20 rounded-2xl shadow-lg backdrop-blur-sm text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300 hover:bg-white/15"
                      />
                    </div>
                  )}
                </div>
              </div>

              {/* Weight */}
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-purple-200 mb-3">What's your weight?</label>
                <div className="flex gap-3">
                  <select
                    name="weightUnit"
                    value={formData.weightUnit}
                    onChange={handleInputChange}
                    className="px-4 py-4 bg-white/10 border border-white/20 rounded-2xl shadow-lg backdrop-blur-sm text-white focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300 hover:bg-white/15"
                  >
                    <option value="kg" className="bg-slate-800 text-white">kg</option>
                    <option value="lbs" className="bg-slate-800 text-white">lbs</option>
                  </select>

                  <input
                    type="number"
                    name={formData.weightUnit === 'kg' ? 'weightKg' : 'weightLbs'}
                    value={formData.weightUnit === 'kg' ? formData.weightKg : formData.weightLbs}
                    onChange={handleInputChange}
                    placeholder={`Weight in ${formData.weightUnit}`}
                    min="1"
                    max={formData.weightUnit === 'kg' ? "500" : "1100"}
                    className="flex-1 px-4 py-4 bg-white/10 border border-white/20 rounded-2xl shadow-lg backdrop-blur-sm text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300 hover:bg-white/15"
                  />
                </div>
              </div>
            </div>

            {/* Last Workout Date */}
            <div className="space-y-2">
              <label htmlFor="lastWorkoutDate" className="block text-sm font-semibold text-purple-200 mb-3">
                When was your last workout?
              </label>
              <input
                type="date"
                id="lastWorkoutDate"
                name="lastWorkoutDate"
                value={formData.lastWorkoutDate}
                onChange={handleInputChange}
                className="w-full px-4 py-4 bg-white/10 border border-white/20 rounded-2xl shadow-lg backdrop-blur-sm text-white focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-300 hover:bg-white/15"
              />
            </div>

            {/* Previous Workout Types */}
            <div className="space-y-4">
              <label className="block text-sm font-semibold text-purple-200 mb-4">
                What types of workouts have you done before?
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {workoutTypeOptions.map((type) => (
                  <label key={type} className="group cursor-pointer">
                    <div className={`relative p-4 rounded-2xl border-2 transition-all duration-300 ${
                      formData.workoutTypes.includes(type)
                        ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 border-purple-400 shadow-lg shadow-purple-500/25'
                        : 'bg-white/5 border-white/20 hover:bg-white/10 hover:border-purple-300'
                    }`}>
                      <input
                        type="checkbox"
                        value={type}
                        checked={formData.workoutTypes.includes(type)}
                        onChange={(e) => handleCheckboxChange(e, 'workoutTypes')}
                        className="sr-only"
                      />
                      <div className="text-center">
                        <div className="text-2xl mb-2">
                          {type === 'Strength' && '🏋️'}
                          {type === 'Cardio' && '🏃'}
                          {type === 'CrossFit' && '⚡'}
                          {type === 'Yoga' && '🧘'}
                          {type === 'Other' && '🎯'}
                        </div>
                        <span className="text-sm font-medium text-white">{type}</span>
                      </div>
                      {formData.workoutTypes.includes(type) && (
                        <div className="absolute top-2 right-2 w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">✓</span>
                        </div>
                      )}
                    </div>
                  </label>
                ))}
              </div>
            </div>

            {/* Goals */}
            <div className="space-y-4">
              <label className="block text-sm font-semibold text-purple-200 mb-4">
                What are your fitness goals?
              </label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {goalOptions.map((goal) => (
                  <label key={goal} className="group cursor-pointer">
                    <div className={`relative p-4 rounded-2xl border-2 transition-all duration-300 ${
                      formData.goals.includes(goal)
                        ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 border-purple-400 shadow-lg shadow-purple-500/25'
                        : 'bg-white/5 border-white/20 hover:bg-white/10 hover:border-purple-300'
                    }`}>
                      <input
                        type="checkbox"
                        value={goal}
                        checked={formData.goals.includes(goal)}
                        onChange={(e) => handleCheckboxChange(e, 'goals')}
                        className="sr-only"
                      />
                      <div className="flex items-center space-x-3">
                        <div className="text-2xl">
                          {goal === 'Regain strength' && '💪'}
                          {goal === 'Lose weight' && '⚖️'}
                          {goal === 'Build endurance' && '🏃‍♂️'}
                          {goal === 'Maintain health' && '❤️'}
                        </div>
                        <span className="text-sm font-medium text-white flex-1">{goal}</span>
                        {formData.goals.includes(goal) && (
                          <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center">
                            <span className="text-white text-xs">✓</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            {/* Submit Button */}
            <div className="pt-8">
              <button
                type="submit"
                className="w-full bg-gradient-to-r from-purple-600 to-pink-600 text-white py-4 px-8 rounded-2xl hover:from-purple-700 hover:to-pink-700 focus:outline-none focus:ring-4 focus:ring-purple-500/50 transition-all duration-300 font-semibold text-lg shadow-xl shadow-purple-500/25 hover:shadow-2xl hover:shadow-purple-500/40 transform hover:scale-[1.02] active:scale-[0.98]"
              >
                <span className="flex items-center justify-center space-x-2">
                  <span>🚀</span>
                  <span>Start My Fitness Journey</span>
                </span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default UserProfileForm;
