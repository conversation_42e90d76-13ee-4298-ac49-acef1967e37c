-- MuscleMemory Database Schema
-- Run this in Supabase SQL Editor (Dashboard → SQL Editor → New Query)

-- NOTE: JWT secret is automatically handled by Supabase - no need to set it manually

-- User Profiles Table
CREATE TABLE user_profiles (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  age INTEGER,
  gender TEXT CHECK (gender IN ('Male', 'Female')),
  height JSONB, -- {unit: 'cm'|'ft', value: number, feet?: number, inches?: number}
  weight JSONB, -- {unit: 'kg'|'lbs', value: number}
  workout_types TEXT[],
  goals TEXT[],
  profile_picture TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id)
);

-- Workouts Table
CREATE TABLE workouts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  exercises JSONB, -- Array of {exercise: string, muscleGroup: string}
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_performed TIMESTAMP WITH TIME ZONE
);

-- Exercise Stats Table
CREATE TABLE exercise_stats (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  exercise TEXT NOT NULL,
  muscle_group TEXT NOT NULL,
  max_weight DECIMAL DEFAULT 0,
  max_reps INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, exercise, muscle_group)
);

-- Recovery Plans Table
CREATE TABLE recovery_plans (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  data JSONB, -- Store the complete recovery plan data
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Row Level Security Policies
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE workouts ENABLE ROW LEVEL SECURITY;
ALTER TABLE exercise_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE recovery_plans ENABLE ROW LEVEL SECURITY;

-- Users can only access their own data
CREATE POLICY "Users can view own profile" ON user_profiles FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update own profile" ON user_profiles FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own profile" ON user_profiles FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view own workouts" ON workouts FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own workouts" ON workouts FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own workouts" ON workouts FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own workouts" ON workouts FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Users can view own exercise stats" ON exercise_stats FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own exercise stats" ON exercise_stats FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own exercise stats" ON exercise_stats FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can view own recovery plans" ON recovery_plans FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own recovery plans" ON recovery_plans FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Functions to automatically update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_exercise_stats_updated_at BEFORE UPDATE ON exercise_stats FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
