import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import NavigationHeader from './NavigationHeader';

interface UserProfile {
  name: string;
  age: number;
  gender: string;
  height: { value: number; unit: string; feet?: number; inches?: number };
  weight: { value: number; unit: string };
  workoutTypes: string[];
  goals: string[];
  submittedAt: string;
}

interface SavedWorkout {
  id: string;
  name: string;
  exercises: Array<{
    muscleGroup: string;
    exercise: string;
  }>;
  createdAt: string;
  lastPerformed?: string;
}

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [savedWorkouts, setSavedWorkouts] = useState<SavedWorkout[]>([]);
  const [showProfileDetails, setShowProfileDetails] = useState(false);
  const [recoveryPlans, setRecoveryPlans] = useState<any[]>([]);

  useEffect(() => {
    // Load user profile
    const storedProfile = localStorage.getItem('userProfileData');
    if (storedProfile) {
      setUserProfile(JSON.parse(storedProfile));
    } else {
      navigate('/'); // Redirect to profile if no data
    }

    // Load saved workouts
    const storedWorkouts = localStorage.getItem('savedWorkouts');
    if (storedWorkouts) {
      setSavedWorkouts(JSON.parse(storedWorkouts));
    }

    // Load recovery plans
    const storedPlans = localStorage.getItem('recoveryPlansHistory');
    if (storedPlans) {
      setRecoveryPlans(JSON.parse(storedPlans));
    }
  }, [navigate]);

  const formatHeight = (height: UserProfile['height']) => {
    if (height.unit === 'cm') {
      return `${height.value} cm`;
    } else {
      return `${height.feet}'${height.inches}"`;
    }
  };

  const formatWeight = (weight: UserProfile['weight']) => {
    return `${weight.value} ${weight.unit}`;
  };

  const calculateBMI = (height: any, weight: any) => {
    if (!height || !weight) return 'N/A';

    // Convert height to meters
    let heightInMeters;
    if (height.unit === 'cm') {
      heightInMeters = height.value / 100;
    } else {
      // Convert feet/inches to meters
      const totalInches = (height.feet || 0) * 12 + (height.inches || 0);
      heightInMeters = totalInches * 0.0254;
    }

    // Convert weight to kg
    let weightInKg;
    if (weight.unit === 'kg') {
      weightInKg = weight.value;
    } else {
      weightInKg = weight.value / 2.205;
    }

    const bmi = weightInKg / (heightInMeters * heightInMeters);
    return bmi.toFixed(1);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const handleStartWorkout = (workout: SavedWorkout) => {
    // Store the selected workout for the active workout page
    localStorage.setItem('activeWorkout', JSON.stringify(workout));
    navigate('/active-workout');
  };

  const handleEditProfile = () => {
    navigate('/profile-edit');
  };

  if (!userProfile) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <div className="text-white text-xl">Loading...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      <NavigationHeader />
      <div className="p-4 md:p-8">
        <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          {userProfile.profilePicture && (
            <div className="w-24 h-24 rounded-full overflow-hidden border-4 border-white/20 mx-auto mb-4">
              <img
                src={userProfile.profilePicture}
                alt="Profile"
                className="w-full h-full object-cover"
              />
            </div>
          )}
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Welcome back, <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400">{userProfile.name}</span>! 💪
          </h1>
          <p className="text-purple-200 text-lg">
            Ready to crush your fitness goals?
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Profile Summary Card */}
          <div className="lg:col-span-1">
            <div className="bg-white/10 backdrop-blur-lg rounded-3xl shadow-2xl border border-white/20 p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-2xl font-bold text-white">Your Profile</h2>
                <button
                  onClick={() => setShowProfileDetails(!showProfileDetails)}
                  className="text-purple-300 hover:text-white transition-colors"
                >
                  {showProfileDetails ? '▲' : '▼'}
                </button>
              </div>
              
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-purple-300">Age:</span>
                  <span className="text-white font-medium">{userProfile.age}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-purple-300">Height:</span>
                  <span className="text-white font-medium">{formatHeight(userProfile.height)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-purple-300">Weight:</span>
                  <span className="text-white font-medium">{formatWeight(userProfile.weight)}</span>
                </div>
                
                {showProfileDetails && (
                  <div className="mt-4 pt-4 border-t border-white/20 space-y-3">
                    <div>
                      <span className="text-purple-300 block mb-1">Profile Created:</span>
                      <span className="text-white text-sm">
                        {userProfile.submittedAt ? formatDate(userProfile.submittedAt) : 'Recently'}
                      </span>
                    </div>
                    <div>
                      <span className="text-purple-300 block mb-1">BMI:</span>
                      <span className="text-white text-sm">
                        {calculateBMI(userProfile.height, userProfile.weight)}
                      </span>
                    </div>
                  </div>
                )}
              </div>
              
              <button
                onClick={handleEditProfile}
                className="w-full mt-6 bg-white/10 text-white py-3 px-4 rounded-2xl hover:bg-white/20 focus:outline-none focus:ring-2 focus:ring-white/25 transition-all duration-300 font-medium border border-white/20"
              >
                ✏️ Edit Profile
              </button>
            </div>
          </div>

          {/* Main Content Area */}
          <div className="lg:col-span-2 space-y-6">
            {/* Quick Actions */}
            <div className="bg-white/10 backdrop-blur-lg rounded-3xl shadow-2xl border border-white/20 p-6">
              <h2 className="text-2xl font-bold text-white mb-6">Quick Actions</h2>
              <div className="grid md:grid-cols-2 gap-4">
                <button
                  onClick={() => navigate('/workout-builder')}
                  className="bg-gradient-to-r from-purple-600 to-pink-600 text-white py-4 px-6 rounded-2xl hover:from-purple-700 hover:to-pink-700 focus:outline-none focus:ring-4 focus:ring-purple-500/50 transition-all duration-300 font-semibold text-lg shadow-xl shadow-purple-500/25 hover:shadow-2xl hover:shadow-purple-500/40 transform hover:scale-[1.02] active:scale-[0.98]"
                >
                  🏗️ Create New Workout
                </button>
                <button
                  onClick={() => navigate('/workout-selection')}
                  className="bg-gradient-to-r from-blue-600 to-cyan-600 text-white py-4 px-6 rounded-2xl hover:from-blue-700 hover:to-cyan-700 focus:outline-none focus:ring-4 focus:ring-blue-500/50 transition-all duration-300 font-semibold text-lg shadow-xl shadow-blue-500/25 hover:shadow-2xl hover:shadow-blue-500/40 transform hover:scale-[1.02] active:scale-[0.98]"
                >
                  📊 Track Previous Performance
                </button>
              </div>
            </div>

            {/* Saved Workouts */}
            <div className="bg-white/10 backdrop-blur-lg rounded-3xl shadow-2xl border border-white/20 p-6">
              <h2 className="text-2xl font-bold text-white mb-6">Your Workouts</h2>
              
              {savedWorkouts.length === 0 ? (
                <div className="text-center py-8">
                  <div className="text-6xl mb-4">🏋️‍♂️</div>
                  <p className="text-purple-200 text-lg mb-4">No workouts created yet</p>
                  <button
                    onClick={() => navigate('/workout-builder')}
                    className="bg-gradient-to-r from-purple-600 to-pink-600 text-white py-3 px-6 rounded-2xl hover:from-purple-700 hover:to-pink-700 focus:outline-none focus:ring-4 focus:ring-purple-500/50 transition-all duration-300 font-medium"
                  >
                    Create Your First Workout
                  </button>
                </div>
              ) : (
                <div className="grid gap-4">
                  {savedWorkouts.map((workout) => (
                    <div key={workout.id} className="bg-white/5 rounded-2xl border border-white/10 p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-xl font-semibold text-white mb-2">{workout.name}</h3>
                          <p className="text-purple-300 text-sm">
                            {workout.exercises.length} exercises • Created {formatDate(workout.createdAt)}
                          </p>
                          {workout.lastPerformed && (
                            <p className="text-purple-400 text-xs mt-1">
                              Last performed: {formatDate(workout.lastPerformed)}
                            </p>
                          )}
                        </div>
                        <div className="flex gap-2">
                          <button
                            onClick={() => navigate(`/workout-edit/${workout.id}`)}
                            className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-3 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500/50 transition-all duration-300 font-medium"
                          >
                            ✏️
                          </button>
                          <button
                            onClick={() => handleStartWorkout(workout)}
                            className="bg-gradient-to-r from-green-600 to-emerald-600 text-white py-2 px-4 rounded-xl hover:from-green-700 hover:to-emerald-700 focus:outline-none focus:ring-2 focus:ring-green-500/50 transition-all duration-300 font-medium"
                          >
                            🚀 Start Workout
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Recovery Plans */}
            <div className="bg-white/10 backdrop-blur-lg rounded-3xl shadow-2xl border border-white/20 p-6">
              <h2 className="text-2xl font-bold text-white mb-6">Recovery Plans</h2>

              {recoveryPlans.length === 0 ? (
                <div className="text-center py-8">
                  <div className="text-6xl mb-4">🎯</div>
                  <p className="text-purple-200 text-lg mb-4">No recovery plans generated yet</p>
                  <button
                    onClick={() => navigate('/workout-selection')}
                    className="bg-gradient-to-r from-purple-600 to-pink-600 text-white py-3 px-6 rounded-2xl hover:from-purple-700 hover:to-pink-700 focus:outline-none focus:ring-4 focus:ring-purple-500/50 transition-all duration-300 font-medium"
                  >
                    Generate Your First Recovery Plan
                  </button>
                </div>
              ) : (
                <div className="grid gap-4">
                  {recoveryPlans.slice(-3).reverse().map((plan) => (
                    <div key={plan.id} className="bg-white/5 rounded-2xl border border-white/10 p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-xl font-semibold text-white mb-2">{plan.name}</h3>
                          <p className="text-purple-300 text-sm">
                            {plan.data.workoutHistory.length} exercises tracked • Created {formatDate(plan.createdAt)}
                          </p>
                          <p className="text-purple-400 text-xs mt-1">
                            Ready for AI analysis and recommendations
                          </p>
                        </div>
                        <div className="flex gap-2">
                          <button
                            onClick={() => {
                              console.log('=== RECOVERY PLAN DATA ===');
                              console.log(JSON.stringify(plan.data, null, 2));
                              alert('Recovery plan data logged to console (F12) - ready for AI analysis!');
                            }}
                            className="bg-gradient-to-r from-green-600 to-emerald-600 text-white py-2 px-4 rounded-xl hover:from-green-700 hover:to-emerald-700 focus:outline-none focus:ring-2 focus:ring-green-500/50 transition-all duration-300 font-medium"
                          >
                            📊 View Data
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
    </div>
  );
};

export default Dashboard;
