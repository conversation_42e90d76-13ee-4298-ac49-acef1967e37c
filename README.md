# ReGyminate 🏋️‍♀️

**ReGyminate** is a fitness assistant web application designed to help users return to their gym routine with personalized guidance. The app collects user information through a clean, responsive form and will eventually provide AI-powered workout recommendations.

## Features

- **Responsive Design**: Mobile-friendly interface built with Tailwind CSS
- **User Profile Collection**: Comprehensive form to gather fitness background and goals
- **Modern Tech Stack**: React + TypeScript + Vite for fast development
- **Clean UI**: Card-style form with gradient background and smooth interactions

## Form Fields

The application collects the following user information:

1. **Personal Info**: Name, Age, Gender
2. **Physical Stats**: Height (cm or ft/in), Weight (kg or lbs)
3. **Fitness History**: Date of last workout, Previous workout types
4. **Goals**: Fitness objectives for returning to the gym

## Tech Stack

- **Frontend**: React 19 with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **Routing**: React Router DOM
- **Package Manager**: npm

## Getting Started

### Prerequisites

- Node.js (version 16 or higher)
- npm

### Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd muscle-memory
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm run dev
   ```

4. Open your browser and navigate to `http://localhost:5173`

### Available Scripts

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm run lint` - Run ESLint for code quality
- `npm run preview` - Preview production build locally

## Project Structure

```
src/
├── components/
│   └── UserProfileForm.tsx    # Main form component
├── pages/
│   └── LandingPage.tsx        # Landing page wrapper
├── App.tsx                    # Main app with routing
├── main.tsx                   # App entry point
└── index.css                  # Tailwind CSS imports
```

## Configuration Files

- `tailwind.config.js` - Tailwind CSS configuration
- `postcss.config.js` - PostCSS configuration for Tailwind
- `vite.config.ts` - Vite build configuration
- `tsconfig.json` - TypeScript configuration

## Future Enhancements

- AI-powered workout recommendations
- Backend integration for data persistence
- User authentication
- Progress tracking
- Workout plan generation
- Social features

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source and available under the [MIT License](LICENSE).
