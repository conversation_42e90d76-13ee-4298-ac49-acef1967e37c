import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { exerciseStatsService, workoutService } from '../services/database';
import type { Workout } from '../lib/supabase';

interface WorkoutSet {
  weight: number;
  reps: number;
  completed: boolean;
  tags?: string[];
}

interface WorkoutExercise {
  exercise: string;
  muscleGroup: string;
  sets: WorkoutSet[];
  notes?: string;
  previousBest?: {
    weight: number;
    reps: number;
  };
}

interface WorkoutSession {
  id?: string;
  name: string;
  exercises: WorkoutExercise[];
  startTime: Date;
  duration: number;
  notes?: string;
}

const WorkoutSessionComponent: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [workoutSession, setWorkoutSession] = useState<WorkoutSession | null>(null);
  const [restTimer, setRestTimer] = useState(0);
  const [isRestTimerActive, setIsRestTimerActive] = useState(false);
  const [workoutStarted, setWorkoutStarted] = useState(false);
  const [elapsedTime, setElapsedTime] = useState(0);

  // Timer for workout duration
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (workoutStarted) {
      interval = setInterval(() => {
        setElapsedTime(prev => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [workoutStarted]);

  // Rest timer
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isRestTimerActive && restTimer > 0) {
      interval = setInterval(() => {
        setRestTimer(prev => {
          if (prev <= 1) {
            setIsRestTimerActive(false);
            // TODO: Add notification sound/vibration
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isRestTimerActive, restTimer]);

  useEffect(() => {
    initializeWorkout();
  }, []);

  const initializeWorkout = async () => {
    // Check if there's a selected workout from localStorage
    const activeWorkout = localStorage.getItem('activeWorkout');
    if (activeWorkout) {
      const workout: Workout = JSON.parse(activeWorkout);
      await startWorkoutFromTemplate(workout);
    } else {
      // Start empty workout
      startEmptyWorkout();
    }
  };

  const startWorkoutFromTemplate = async (template: Workout) => {
    if (!user) return;

    try {
      // Load previous performance for each exercise
      const exercisesWithHistory: WorkoutExercise[] = await Promise.all(
        template.exercises.map(async (exercise: any) => {
          // TODO: Get specific exercise stats
          const allStats = await exerciseStatsService.getStats(user.id);
          const stats = allStats.find(s => s.exercise === exercise.exercise && s.muscle_group === exercise.muscleGroup);
          
          return {
            exercise: exercise.exercise,
            muscleGroup: exercise.muscleGroup,
            sets: [{ weight: stats?.max_weight || 0, reps: stats?.max_reps || 0, completed: false }],
            previousBest: stats ? {
              weight: stats.max_weight,
              reps: stats.max_reps
            } : undefined
          };
        })
      );

      const session: WorkoutSession = {
        name: template.name,
        exercises: exercisesWithHistory,
        startTime: new Date(),
        duration: 0
      };

      setWorkoutSession(session);
      setWorkoutStarted(true);
    } catch (error) {
      console.error('Error starting workout from template:', error);
    }
  };

  const startEmptyWorkout = () => {
    const session: WorkoutSession = {
      name: 'Quick Workout',
      exercises: [],
      startTime: new Date(),
      duration: 0
    };

    setWorkoutSession(session);
    setWorkoutStarted(true);
  };

  const addSet = (exerciseIndex: number) => {
    if (!workoutSession) return;

    const updatedSession = { ...workoutSession };
    const lastSet = updatedSession.exercises[exerciseIndex].sets.slice(-1)[0];
    
    updatedSession.exercises[exerciseIndex].sets.push({
      weight: lastSet?.weight || 0,
      reps: lastSet?.reps || 0,
      completed: false
    });

    setWorkoutSession(updatedSession);
  };

  const updateSet = (exerciseIndex: number, setIndex: number, field: 'weight' | 'reps', value: number) => {
    if (!workoutSession) return;

    const updatedSession = { ...workoutSession };
    updatedSession.exercises[exerciseIndex].sets[setIndex][field] = value;
    setWorkoutSession(updatedSession);
  };

  const completeSet = (exerciseIndex: number, setIndex: number) => {
    if (!workoutSession) return;

    const updatedSession = { ...workoutSession };
    updatedSession.exercises[exerciseIndex].sets[setIndex].completed = true;
    setWorkoutSession(updatedSession);

    // Start rest timer (default 90 seconds)
    setRestTimer(90);
    setIsRestTimerActive(true);
  };

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const finishWorkout = async () => {
    if (!workoutSession || !user) return;

    try {
      // Save workout to database
      const workoutData = {
        user_id: user.id,
        name: workoutSession.name,
        exercises: workoutSession.exercises,
        created_at: new Date().toISOString(),
        last_performed: new Date().toISOString()
      };

      await workoutService.createWorkout(workoutData);

      // Update exercise stats
      for (const exercise of workoutSession.exercises) {
        const completedSets = exercise.sets.filter(set => set.completed);
        if (completedSets.length > 0) {
          const maxWeight = Math.max(...completedSets.map(set => set.weight));
          const maxReps = Math.max(...completedSets.map(set => set.reps));

          await exerciseStatsService.updateStats(
            user.id,
            exercise.exercise,
            exercise.muscleGroup,
            maxWeight,
            maxReps
          );
        }
      }

      // Clear active workout and navigate back
      localStorage.removeItem('activeWorkout');
      navigate('/dashboard');
    } catch (error) {
      console.error('Error saving workout:', error);
    }
  };

  if (!workoutSession) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-white text-xl">Loading workout...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header with timer */}
      <div className="bg-black/20 backdrop-blur-sm border-b border-white/10 p-4">
        <div className="flex justify-between items-center max-w-4xl mx-auto">
          <button
            onClick={() => navigate('/dashboard')}
            className="text-white/70 hover:text-white transition-colors"
          >
            ← Back
          </button>
          
          <div className="text-center">
            <h1 className="text-white text-xl font-bold">{workoutSession.name}</h1>
            <div className="text-purple-400 text-sm">{formatTime(elapsedTime)}</div>
          </div>

          <button
            onClick={finishWorkout}
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            Finish
          </button>
        </div>
      </div>

      {/* Rest Timer */}
      {isRestTimerActive && (
        <div className="bg-orange-600/90 backdrop-blur-sm p-3 text-center">
          <div className="text-white font-bold">Rest Timer: {formatTime(restTimer)}</div>
        </div>
      )}

      {/* Workout Content */}
      <div className="p-4 max-w-4xl mx-auto">
        {workoutSession.exercises.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-white/70 mb-4">No exercises added yet</div>
            <button
              onClick={() => alert('Add Exercise feature coming soon!')}
              className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg"
            >
              Add First Exercise
            </button>
          </div>
        ) : (
          <div className="space-y-6">
            {workoutSession.exercises.map((exercise, exerciseIndex) => (
              <div key={exerciseIndex} className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                <div className="flex justify-between items-center mb-4">
                  <div>
                    <h3 className="text-white text-lg font-semibold">{exercise.exercise}</h3>
                    <p className="text-purple-400 text-sm">{exercise.muscleGroup}</p>
                    {exercise.previousBest && (
                      <p className="text-white/60 text-xs">
                        Previous: {exercise.previousBest.weight}kg × {exercise.previousBest.reps}
                      </p>
                    )}
                  </div>
                </div>

                {/* Sets */}
                <div className="space-y-2">
                  <div className="grid grid-cols-4 gap-2 text-white/70 text-sm font-medium">
                    <div>Set</div>
                    <div>Weight</div>
                    <div>Reps</div>
                    <div>✓</div>
                  </div>
                  
                  {exercise.sets.map((set, setIndex) => (
                    <div key={setIndex} className="grid grid-cols-4 gap-2 items-center">
                      <div className="text-white font-medium">{setIndex + 1}</div>
                      
                      <input
                        type="number"
                        value={set.weight}
                        onChange={(e) => updateSet(exerciseIndex, setIndex, 'weight', Number(e.target.value))}
                        className="bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white text-center"
                        disabled={set.completed}
                      />
                      
                      <input
                        type="number"
                        value={set.reps}
                        onChange={(e) => updateSet(exerciseIndex, setIndex, 'reps', Number(e.target.value))}
                        className="bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white text-center"
                        disabled={set.completed}
                      />
                      
                      <button
                        onClick={() => completeSet(exerciseIndex, setIndex)}
                        disabled={set.completed}
                        className={`w-8 h-8 rounded-full border-2 flex items-center justify-center transition-colors ${
                          set.completed 
                            ? 'bg-green-600 border-green-600 text-white' 
                            : 'border-white/30 hover:border-green-400'
                        }`}
                      >
                        {set.completed && '✓'}
                      </button>
                    </div>
                  ))}
                </div>

                <button
                  onClick={() => addSet(exerciseIndex)}
                  className="mt-3 text-purple-400 hover:text-purple-300 text-sm font-medium"
                >
                  + Add Set
                </button>
              </div>
            ))}

            <button
              onClick={() => alert('Add Exercise feature coming soon!')}
              className="w-full bg-purple-600/20 border-2 border-dashed border-purple-400 text-purple-400 py-4 rounded-xl hover:bg-purple-600/30 transition-colors"
            >
              + Add Exercise
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default WorkoutSessionComponent;
