<!DOCTYPE html>
<html>
<head>
    <title>Create PWA Icons</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        canvas { border: 1px solid #ccc; margin: 10px; }
        .icon-container { display: inline-block; margin: 10px; text-align: center; }
    </style>
</head>
<body>
    <h1>MuscleMemory PWA Icons Generator</h1>
    <p>This will generate the required PWA icons. Right-click each canvas and "Save image as..."</p>
    
    <div class="icon-container">
        <h3>192x192 Icon</h3>
        <canvas id="icon192" width="192" height="192"></canvas>
        <br>Save as: <strong>icon-192.png</strong>
    </div>
    
    <div class="icon-container">
        <h3>512x512 Icon</h3>
        <canvas id="icon512" width="512" height="512"></canvas>
        <br>Save as: <strong>icon-512.png</strong>
    </div>

    <script>
        function createIcon(canvasId, size) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            
            // Background gradient
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#8b5cf6'); // Purple
            gradient.addColorStop(1, '#ec4899'); // Pink
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // Add rounded corners
            ctx.globalCompositeOperation = 'destination-in';
            ctx.beginPath();
            ctx.roundRect(0, 0, size, size, size * 0.15);
            ctx.fill();
            
            // Reset composite operation
            ctx.globalCompositeOperation = 'source-over';
            
            // Add muscle/dumbbell icon
            ctx.fillStyle = 'white';
            ctx.font = `bold ${size * 0.4}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('💪', size/2, size/2);
            
            // Add text
            ctx.font = `bold ${size * 0.08}px Arial`;
            ctx.fillText('MuscleMemory', size/2, size * 0.85);
        }
        
        // Create both icons
        createIcon('icon192', 192);
        createIcon('icon512', 512);
    </script>
</body>
</html>
