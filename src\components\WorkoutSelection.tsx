import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

interface WorkoutData {
  muscleGroup: string;
  exercise: string;
  previousMaxWeight: number;
  previousReps: number;
  daysSinceLastWorkout: number;
}

interface UserProfile {
  name: string;
  age: number;
  gender: string;
  height: any;
  weight: any;
  lastWorkoutDate: string;
  workoutTypes: string[];
  goals: string[];
  submittedAt: string;
}

const WorkoutSelection: React.FC = () => {
  const navigate = useNavigate();
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [selectedWorkouts, setSelectedWorkouts] = useState<WorkoutData[]>([]);
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set());

  const muscleGroups = {
    'Chest': ['Bench Press', 'Incline Press', 'Dumbbell Flyes', 'Push-ups', 'Dips'],
    'Back': ['Pull-ups', 'Lat Pulldown', 'Rows', 'Deadlifts', 'T-Bar Row'],
    'Shoulders': ['Overhead Press', 'Lateral Raises', 'Front Raises', 'Rear Delt Flyes', 'Shrugs'],
    'Arms': ['Bicep Curls', 'Tricep Extensions', 'Hammer Curls', 'Close-Grip Press', 'Preacher Curls'],
    'Legs': ['Squats', 'Leg Press', 'Lunges', 'Leg Curls', 'Calf Raises'],
    'Core': ['Planks', 'Crunches', 'Russian Twists', 'Leg Raises', 'Dead Bug']
  };

  useEffect(() => {
    const storedData = localStorage.getItem('userProfileData');
    if (storedData) {
      setUserProfile(JSON.parse(storedData));
    } else {
      navigate('/'); // Redirect back if no profile data
    }
  }, [navigate]);

  const toggleMuscleGroup = (group: string) => {
    const newExpanded = new Set(expandedGroups);
    if (newExpanded.has(group)) {
      newExpanded.delete(group);
      // Remove all workouts from this group when collapsing
      setSelectedWorkouts(prev => prev.filter(w => w.muscleGroup !== group));
    } else {
      newExpanded.add(group);
    }
    setExpandedGroups(newExpanded);
  };

  const updateWorkoutData = (muscleGroup: string, exercise: string, field: keyof Omit<WorkoutData, 'muscleGroup' | 'exercise'>, value: number) => {
    setSelectedWorkouts(prev => {
      const existing = prev.find(w => w.muscleGroup === muscleGroup && w.exercise === exercise);
      if (existing) {
        return prev.map(w => 
          w.muscleGroup === muscleGroup && w.exercise === exercise 
            ? { ...w, [field]: value }
            : w
        );
      } else {
        return [...prev, {
          muscleGroup,
          exercise,
          previousMaxWeight: field === 'previousMaxWeight' ? value : 0,
          previousReps: field === 'previousReps' ? value : 0,
          daysSinceLastWorkout: field === 'daysSinceLastWorkout' ? value : 0
        }];
      }
    });
  };

  const getWorkoutData = (muscleGroup: string, exercise: string) => {
    return selectedWorkouts.find(w => w.muscleGroup === muscleGroup && w.exercise === exercise);
  };

  const handleSubmit = () => {
    const completeData = {
      userProfile,
      workoutHistory: selectedWorkouts.filter(w => 
        w.previousMaxWeight > 0 && w.previousReps > 0 && w.daysSinceLastWorkout >= 0
      ),
      submittedAt: new Date().toISOString()
    };

    console.log('=== COMPLETE WORKOUT DATA FOR AI ===');
    console.log(JSON.stringify(completeData, null, 2));
    
    alert('Workout data submitted! Check the browser console (F12) for the complete JSON data to feed into AI for your recovery plan.');
  };

  if (!userProfile) {
    return <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
      <div className="text-white text-xl">Loading...</div>
    </div>;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 py-8 px-4 pb-24 md:pb-16">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white/10 backdrop-blur-lg rounded-3xl shadow-2xl border border-white/20 p-8 md:p-12">
          <div className="text-center mb-8">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
              Welcome back, <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400">{userProfile.name}</span>! 💪
            </h1>
            <p className="text-purple-200 text-lg">
              Select the exercises you used to do and tell us about your previous performance
            </p>
          </div>

          <div className="space-y-6">
            {Object.entries(muscleGroups).map(([group, exercises]) => (
              <div key={group} className="bg-white/5 rounded-2xl border border-white/10 overflow-hidden">
                <button
                  onClick={() => toggleMuscleGroup(group)}
                  className="w-full p-6 text-left flex items-center justify-between hover:bg-white/10 transition-all duration-300"
                >
                  <div className="flex items-center space-x-4">
                    <span className="text-2xl">
                      {group === 'Chest' && '💪'}
                      {group === 'Back' && '🏋️'}
                      {group === 'Shoulders' && '🤸'}
                      {group === 'Arms' && '💪'}
                      {group === 'Legs' && '🦵'}
                      {group === 'Core' && '🔥'}
                    </span>
                    <h3 className="text-xl font-semibold text-white">{group}</h3>
                  </div>
                  <span className={`text-white transition-transform duration-300 ${expandedGroups.has(group) ? 'rotate-180' : ''}`}>
                    ▼
                  </span>
                </button>

                {expandedGroups.has(group) && (
                  <div className="p-6 pt-0 space-y-4">
                    {exercises.map(exercise => {
                      const workoutData = getWorkoutData(group, exercise);
                      return (
                        <div key={exercise} className="bg-white/5 rounded-xl p-4 border border-white/10">
                          <h4 className="text-lg font-medium text-purple-200 mb-4">{exercise}</h4>
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                              <label className="block text-sm text-purple-300 mb-2">Previous Max Weight (lbs/kg)</label>
                              <input
                                type="number"
                                value={workoutData?.previousMaxWeight || ''}
                                onChange={(e) => updateWorkoutData(group, exercise, 'previousMaxWeight', Number(e.target.value))}
                                placeholder="0"
                                min="0"
                                className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-400"
                              />
                            </div>
                            <div>
                              <label className="block text-sm text-purple-300 mb-2">Previous Reps</label>
                              <input
                                type="number"
                                value={workoutData?.previousReps || ''}
                                onChange={(e) => updateWorkoutData(group, exercise, 'previousReps', Number(e.target.value))}
                                placeholder="0"
                                min="0"
                                className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-400"
                              />
                            </div>
                            <div>
                              <label className="block text-sm text-purple-300 mb-2">Days Since Last Workout</label>
                              <input
                                type="number"
                                value={workoutData?.daysSinceLastWorkout || ''}
                                onChange={(e) => updateWorkoutData(group, exercise, 'daysSinceLastWorkout', Number(e.target.value))}
                                placeholder="0"
                                min="0"
                                className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-400"
                              />
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="mt-8 flex gap-4">
            <button
              onClick={() => navigate('/')}
              className="flex-1 bg-white/10 text-white py-4 px-8 rounded-2xl hover:bg-white/20 focus:outline-none focus:ring-4 focus:ring-white/25 transition-all duration-300 font-semibold text-lg border border-white/20"
            >
              ← Back to Profile
            </button>
            <button
              onClick={handleSubmit}
              className="flex-1 bg-gradient-to-r from-purple-600 to-pink-600 text-white py-4 px-8 rounded-2xl hover:from-purple-700 hover:to-pink-700 focus:outline-none focus:ring-4 focus:ring-purple-500/50 transition-all duration-300 font-semibold text-lg shadow-xl shadow-purple-500/25 hover:shadow-2xl hover:shadow-purple-500/40 transform hover:scale-[1.02] active:scale-[0.98]"
            >
              🚀 Generate Recovery Plan
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WorkoutSelection;
