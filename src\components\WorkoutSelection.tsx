import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import NavigationHeader from './NavigationHeader';

interface WorkoutData {
  muscleGroup: string;
  exercise: string;
  previousMaxWeight: number; // Max weight lifted in a single set
  previousReps: number;      // Max reps completed in a single set
  daysSinceLastWorkout: number;
}

interface UserProfile {
  name: string;
  age: number;
  gender: string;
  height: any;
  weight: any;
  lastWorkoutDate: string;
  workoutTypes: string[];
  goals: string[];
  submittedAt: string;
}

const WorkoutSelection: React.FC = () => {
  const navigate = useNavigate();
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [selectedWorkouts, setSelectedWorkouts] = useState<WorkoutData[]>([]);
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set());
  const [expandedExercises, setExpandedExercises] = useState<Set<string>>(new Set());

  const muscleGroups = {
    'Chest': ['Bench Press', 'Incline Press', 'Dumbbell Flyes', 'Push-ups', 'Dips'],
    'Back': ['Pull-ups', 'Lat Pulldown', 'Rows', 'Deadlifts', 'T-Bar Row'],
    'Shoulders': ['Overhead Press', 'Lateral Raises', 'Front Raises', 'Rear Delt Flyes', 'Shrugs'],
    'Arms': ['Bicep Curls', 'Tricep Extensions', 'Hammer Curls', 'Close-Grip Press', 'Preacher Curls'],
    'Legs': ['Squats', 'Leg Press', 'Lunges', 'Leg Curls', 'Calf Raises'],
    'Core': ['Planks', 'Crunches', 'Russian Twists', 'Leg Raises', 'Dead Bug']
  };

  useEffect(() => {
    const storedData = localStorage.getItem('userProfileData');
    if (storedData) {
      setUserProfile(JSON.parse(storedData));
    } else {
      navigate('/'); // Redirect back if no profile data
    }

    // Load saved workout data if it exists
    const savedWorkouts = localStorage.getItem('workoutSelectionData');
    if (savedWorkouts) {
      setSelectedWorkouts(JSON.parse(savedWorkouts));
    }

    // Load saved expanded states
    const savedExpandedGroups = localStorage.getItem('expandedGroups');
    if (savedExpandedGroups) {
      setExpandedGroups(new Set(JSON.parse(savedExpandedGroups)));
    }

    const savedExpandedExercises = localStorage.getItem('expandedExercises');
    if (savedExpandedExercises) {
      setExpandedExercises(new Set(JSON.parse(savedExpandedExercises)));
    }
  }, [navigate]);

  const toggleMuscleGroup = (group: string) => {
    const newExpanded = new Set(expandedGroups);
    if (newExpanded.has(group)) {
      newExpanded.delete(group);
      // Also collapse all exercises in this group
      const newExpandedExercises = new Set(expandedExercises);
      muscleGroups[group as keyof typeof muscleGroups].forEach(exercise => {
        newExpandedExercises.delete(`${group}-${exercise}`);
      });
      setExpandedExercises(newExpandedExercises);
      localStorage.setItem('expandedExercises', JSON.stringify([...newExpandedExercises]));
    } else {
      newExpanded.add(group);
    }
    setExpandedGroups(newExpanded);
    localStorage.setItem('expandedGroups', JSON.stringify([...newExpanded]));
  };

  const toggleExercise = (group: string, exercise: string) => {
    const exerciseKey = `${group}-${exercise}`;
    const newExpanded = new Set(expandedExercises);
    if (newExpanded.has(exerciseKey)) {
      newExpanded.delete(exerciseKey);
    } else {
      newExpanded.add(exerciseKey);
    }
    setExpandedExercises(newExpanded);
    localStorage.setItem('expandedExercises', JSON.stringify([...newExpanded]));
  };

  const updateWorkoutData = (muscleGroup: string, exercise: string, field: keyof Omit<WorkoutData, 'muscleGroup' | 'exercise'>, value: number) => {
    setSelectedWorkouts(prev => {
      const existing = prev.find(w => w.muscleGroup === muscleGroup && w.exercise === exercise);
      let newWorkouts;
      if (existing) {
        newWorkouts = prev.map(w =>
          w.muscleGroup === muscleGroup && w.exercise === exercise
            ? { ...w, [field]: value }
            : w
        );
      } else {
        newWorkouts = [...prev, {
          muscleGroup,
          exercise,
          previousMaxWeight: field === 'previousMaxWeight' ? value : 0,
          previousReps: field === 'previousReps' ? value : 0,
          daysSinceLastWorkout: field === 'daysSinceLastWorkout' ? value : 0
        }];
      }
      // Save to localStorage whenever data changes
      localStorage.setItem('workoutSelectionData', JSON.stringify(newWorkouts));
      return newWorkouts;
    });
  };

  const getWorkoutData = (muscleGroup: string, exercise: string) => {
    return selectedWorkouts.find(w => w.muscleGroup === muscleGroup && w.exercise === exercise);
  };

  const handleSubmit = () => {
    const completeData = {
      userProfile,
      workoutHistory: selectedWorkouts.filter(w =>
        w.previousMaxWeight > 0 && w.previousReps > 0 && w.daysSinceLastWorkout >= 0
      ),
      submittedAt: new Date().toISOString()
    };

    // Save recovery plan data to localStorage for database storage later
    localStorage.setItem('recoveryPlanData', JSON.stringify(completeData));

    // Also save to a recovery plans history
    const existingPlans = JSON.parse(localStorage.getItem('recoveryPlansHistory') || '[]');
    const newPlan = {
      id: Date.now().toString(),
      name: `Recovery Plan ${new Date().toLocaleDateString()}`,
      data: completeData,
      createdAt: new Date().toISOString()
    };
    existingPlans.push(newPlan);
    localStorage.setItem('recoveryPlansHistory', JSON.stringify(existingPlans));

    console.log('=== COMPLETE WORKOUT DATA FOR AI ===');
    console.log(JSON.stringify(completeData, null, 2));

    // Navigate back to dashboard
    navigate('/dashboard');
  };

  if (!userProfile) {
    return <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
      <div className="text-white text-xl">Loading...</div>
    </div>;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <NavigationHeader />
      <div className="py-8 px-4 pb-24 md:pb-16">
        <div className="max-w-4xl mx-auto">
        <div className="bg-white/10 backdrop-blur-lg rounded-3xl shadow-2xl border border-white/20 p-8 md:p-12">
          <div className="text-center mb-8">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
              Welcome back, <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400">{userProfile.name}</span>! 💪
            </h1>
            <p className="text-purple-200 text-lg">
              Select the exercises you used to do and tell us about your previous performance
            </p>
            <div className="mt-4 bg-blue-500/10 border border-blue-500/20 rounded-xl p-4">
              <div className="text-blue-300 text-sm font-medium mb-1">💡 How to track reps:</div>
              <div className="text-blue-200 text-sm">
                Enter your best single-set performance. For example, if you did 3 sets of 8, 10, 8 reps, enter "10" as your max reps per set.
              </div>
            </div>
          </div>

          <div className="space-y-6">
            {Object.entries(muscleGroups).map(([group, exercises]) => {
              const exercisesWithData = exercises.filter(exercise => {
                const workoutData = getWorkoutData(group, exercise);
                return workoutData && (workoutData.previousMaxWeight > 0 || workoutData.previousReps > 0 || workoutData.daysSinceLastWorkout > 0);
              });

              return (
                <div key={group} className="bg-white/5 rounded-2xl border border-white/10 overflow-hidden">
                  <button
                    onClick={() => toggleMuscleGroup(group)}
                    className="w-full p-6 text-left flex items-center justify-between hover:bg-white/10 transition-all duration-300"
                  >
                    <div className="flex items-center space-x-4">
                      <span className="text-2xl">
                        {group === 'Chest' && '💪'}
                        {group === 'Back' && '🏋️'}
                        {group === 'Shoulders' && '🤸'}
                        {group === 'Arms' && '💪'}
                        {group === 'Legs' && '🦵'}
                        {group === 'Core' && '🔥'}
                      </span>
                      <div className="flex items-center space-x-3">
                        <h3 className="text-xl font-semibold text-white">{group}</h3>
                        {exercisesWithData.length > 0 && (
                          <span className="px-2 py-1 bg-purple-500/20 text-purple-300 text-sm rounded-full border border-purple-500/30">
                            {exercisesWithData.length} exercise{exercisesWithData.length !== 1 ? 's' : ''}
                          </span>
                        )}
                      </div>
                    </div>
                    <span className={`text-white transition-transform duration-300 ${expandedGroups.has(group) ? 'rotate-180' : ''}`}>
                      ▼
                    </span>
                  </button>

                {expandedGroups.has(group) && (
                  <div className="p-6 pt-0 space-y-3">
                    {exercises.map(exercise => {
                      const exerciseKey = `${group}-${exercise}`;
                      const isExerciseExpanded = expandedExercises.has(exerciseKey);
                      const workoutData = getWorkoutData(group, exercise);
                      const hasData = workoutData && (workoutData.previousMaxWeight > 0 || workoutData.previousReps > 0 || workoutData.daysSinceLastWorkout > 0);

                      return (
                        <div key={exercise} className="bg-white/5 rounded-xl border border-white/10 overflow-hidden">
                          <button
                            onClick={() => toggleExercise(group, exercise)}
                            className="w-full p-4 text-left flex items-center justify-between hover:bg-white/10 transition-all duration-300"
                          >
                            <div className="flex items-center space-x-3">
                              <span className="text-lg font-medium text-purple-200">{exercise}</span>
                              {hasData && (
                                <span className="px-2 py-1 bg-purple-500/20 text-purple-300 text-xs rounded-full border border-purple-500/30">
                                  ✓ Data entered
                                </span>
                              )}
                            </div>
                            <span className={`text-purple-300 transition-transform duration-300 ${isExerciseExpanded ? 'rotate-180' : ''}`}>
                              ▼
                            </span>
                          </button>

                          {isExerciseExpanded && (
                            <div className="p-4 pt-0">
                              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                  <label className="block text-sm text-purple-300 mb-2">Previous Max Weight (lbs/kg)</label>
                                  <input
                                    type="number"
                                    value={workoutData?.previousMaxWeight || ''}
                                    onChange={(e) => updateWorkoutData(group, exercise, 'previousMaxWeight', Number(e.target.value))}
                                    placeholder="0"
                                    min="0"
                                    className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-400"
                                  />
                                </div>
                                <div>
                                  <label className="block text-sm text-purple-300 mb-2">Previous Max Reps (per set)</label>
                                  <input
                                    type="number"
                                    value={workoutData?.previousReps || ''}
                                    onChange={(e) => updateWorkoutData(group, exercise, 'previousReps', Number(e.target.value))}
                                    placeholder="0"
                                    min="0"
                                    className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-400"
                                  />
                                </div>
                                <div>
                                  <label className="block text-sm text-purple-300 mb-2">Days Since Last Workout</label>
                                  <input
                                    type="number"
                                    value={workoutData?.daysSinceLastWorkout || ''}
                                    onChange={(e) => updateWorkoutData(group, exercise, 'daysSinceLastWorkout', Number(e.target.value))}
                                    placeholder="0"
                                    min="0"
                                    className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-400"
                                  />
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
              );
            })}
          </div>

          <div className="mt-8 flex justify-center">
            <button
              onClick={handleSubmit}
              className="bg-gradient-to-r from-purple-600 to-pink-600 text-white py-4 px-12 rounded-2xl hover:from-purple-700 hover:to-pink-700 focus:outline-none focus:ring-4 focus:ring-purple-500/50 transition-all duration-300 font-semibold text-lg shadow-xl shadow-purple-500/25 hover:shadow-2xl hover:shadow-purple-500/40 transform hover:scale-[1.02] active:scale-[0.98]"
            >
              🚀 Generate Recovery Plan
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  );
};

export default WorkoutSelection;
