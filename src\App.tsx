
import { useEffect, useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from './contexts/AuthContext';
import { Auth } from './components/Auth';
import LandingPage from './pages/LandingPage';
import WorkoutSelection from './components/WorkoutSelection';
import Dashboard from './components/Dashboard';
import WorkoutBuilder from './components/WorkoutBuilder';
import ActiveWorkout from './components/ActiveWorkout';
import WorkoutSession from './components/WorkoutSession';
import ProfileEdit from './components/ProfileEdit';
import WorkoutEdit from './components/WorkoutEdit';


function App() {
  const { user, loading, userProfile } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-white text-xl">Loading...</div>
      </div>
    );
  }

  if (!user) {
    return <Auth />;
  }

  return (
    <Router>
      <Routes>
        {/* Smart routing: Dashboard if profile exists, profile setup if not */}
        <Route
          path="/"
          element={
            userProfile ? <Navigate to="/dashboard" replace /> : <LandingPage />
          }
        />
        <Route path="/profile-setup" element={<LandingPage />} />
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/workout-builder" element={<WorkoutBuilder />} />
        <Route path="/workout-selection" element={<WorkoutSelection />} />
        <Route path="/active-workout" element={<ActiveWorkout />} />
        <Route path="/workout-session" element={<WorkoutSession />} />
        <Route path="/profile-edit" element={<ProfileEdit />} />
        <Route path="/workout-edit/:workoutId" element={<WorkoutEdit />} />
      </Routes>
    </Router>
  );
}

export default App;
