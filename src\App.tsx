
import { useEffect, useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from './contexts/AuthContext';
import { Auth } from './components/Auth';
import LandingPage from './pages/LandingPage';
import WorkoutSelection from './components/WorkoutSelection';
import Dashboard from './components/Dashboard';
import WorkoutBuilder from './components/WorkoutBuilder';
import ActiveWorkout from './components/ActiveWorkout';
import WorkoutSession from './components/WorkoutSession';
import ProfileEdit from './components/ProfileEdit';
import WorkoutEdit from './components/WorkoutEdit';
import { profileService } from './services/database';

function App() {
  const { user, loading } = useAuth();
  const [hasProfile, setHasProfile] = useState<boolean | null>(null);
  const [checkingProfile, setCheckingProfile] = useState(true);

  useEffect(() => {
    const checkUserProfile = async () => {
      if (user) {
        console.log('🔍 Checking profile for user:', user.id);
        try {
          const profile = await profileService.getProfile(user.id);
          console.log('📋 Profile found:', !!profile, profile);
          setHasProfile(!!profile);
        } catch (error) {
          console.error('❌ Error checking profile:', error);
          setHasProfile(false);
        }
      } else {
        console.log('👤 No user found');
      }
      setCheckingProfile(false);
    };

    if (user) {
      checkUserProfile();
    } else {
      setCheckingProfile(false);
    }
  }, [user]);

  if (loading || checkingProfile) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-white text-xl">Loading...</div>
      </div>
    );
  }

  if (!user) {
    return <Auth />;
  }

  return (
    <Router>
      <Routes>
        {/* Smart routing: Dashboard if profile exists, profile setup if not */}
        <Route
          path="/"
          element={
            (() => {
              console.log('🚦 Routing decision: hasProfile =', hasProfile);
              return hasProfile ? <Navigate to="/dashboard" replace /> : <LandingPage />;
            })()
          }
        />
        <Route path="/profile-setup" element={<LandingPage />} />
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/workout-builder" element={<WorkoutBuilder />} />
        <Route path="/workout-selection" element={<WorkoutSelection />} />
        <Route path="/active-workout" element={<ActiveWorkout />} />
        <Route path="/workout-session" element={<WorkoutSession />} />
        <Route path="/profile-edit" element={<ProfileEdit />} />
        <Route path="/workout-edit/:workoutId" element={<WorkoutEdit />} />
      </Routes>
    </Router>
  );
}

export default App;
